# 🐾 东方妖怪捉宠放置游戏开发规范

> 🎮 **项目**: 东方妖怪捉宠放置游戏 | 🛠️ **引擎**: Cocos Creator 3.8.6 | 📱 **平台**: 微信小程序 + 抖音小程序
> 🌸 **题材**: 东方妖怪世界观 | ⚔️ **玩法**: 挂机放置 + 妖怪捕捉养成 | 🏭 **特色**: 行动条系统

## 🚨 重要更新：前后端分离架构

**📅 更新时间**: 2025年7月22日
**🔄 架构变更**: 从纯客户端架构升级为前后端分离架构

### 🎯 新架构概述
- **前端**: Cocos Creator + TypeScript (客户端渲染、UI交互、本地缓存)
- **后端**: Node.js + MongoDB (业务逻辑、数据存储、实时通信)
- **通信**: RESTful API + WebSocket (数据同步、实时功能)

### 📋 架构规划文档
- [🔄 前后端分离规划](./FRONTEND_BACKEND_SEPARATION_PLAN.md) - 分离架构设计、功能分层
- [📁 项目重组建议](./PROJECT_STRUCTURE_REORGANIZATION.md) - 文档结构重组方案
- [🛣️ 实施路线图](./IMPLEMENTATION_ROADMAP.md) - 分阶段实施计划、里程碑

---

## 📋 文档导航

本文档集合详细规定了东方妖怪捉宠放置游戏的完整开发规范，涵盖了从项目架构设计到部署发布的全流程。

### 🎮 **游戏特色系统规范**
- **🐾 妖怪系统规范** - 捕捉、养成、进化系统的开发标准
- **⏱️ 行动条系统规范** - 统一时间管理机制的实现规范
- **🏭 生产系统规范** - 资源采集和物品制作的开发标准
- **💤 挂机系统规范** - 离线收益和自动化的实现规范

### 📚 规范文档列表

#### 🎯 核心规范
- **[项目概述](./project-overview.md)** - 项目背景、核心特性、技术栈
- **[技术架构](./technical-architecture.md)** - 整体架构设计、系统分层
- **[项目结构](./project-structure.md)** - 目录结构规范、文件组织

#### 💻 开发规范
- **[代码标准](./code-standards.md)** - TypeScript编码规范、命名约定
- **[组件架构](./component-architecture.md)** - 组件设计模式、基类体系
- **[资源管理](./resource-management.md)** - 资源命名、组织、优化规范

#### 📊 系统设计
- **[数据管理](./data-management.md)** - 数据架构、存储、缓存策略
- **[网络通信](./network-architecture.md)** - 网络架构、协议设计、同步机制
- **[UI设计规范](./ui-guidelines.md)** - UI组件、布局、交互设计

#### 📱 平台优化
- **[小程序优化](./miniprogram-optimization.md)** - 平台限制、性能优化、适配策略

#### 🔄 开发流程
- **[迁移流程规范](./migration-process.md)** - Godot到Cocos Creator迁移原则和流程
- **[开发工作流](./development-workflow.md)** - 版本控制、协作流程、代码审查
- **[测试规范](./testing-procedures.md)** - 测试策略、自动化测试、质量保证
- **[部署发布](./deployment-process.md)** - 构建配置、发布流程、环境管理

## 🎮 项目简介

### 项目背景
本项目是一款**中国江湖武侠风多人放置游戏**，从Godot 4.4引擎迁移至Cocos Creator 3.8.6，目标平台为微信小程序和抖音小程序。

### 核心特性
- **武侠主题**: 门派系统、武功技能、江湖恩怨、修炼境界
- **放置玩法**: 离线收益、自动战斗、挂机修炼、资源收集
- **多人互动**: 帮派社交、好友系统、实时聊天、江湖排行
- **核心玩法**: 战斗系统、制作系统、交易系统、任务系统

### 技术栈概览
```typescript
const TechStack = {
    engine: 'Cocos Creator 3.8.6',
    language: 'TypeScript 4.x',
    platforms: ['微信小程序', '抖音小程序'],
    cloud: {
        wechat: '微信云开发',
        douyin: '抖音云服务'
    }
};
```

## 📖 使用指南

### 新团队成员入门
1. 首先阅读 **[项目概述](./project-overview.md)** 了解项目背景
2. 学习 **[技术架构](./technical-architecture.md)** 掌握整体设计
3. 熟悉 **[项目结构](./project-structure.md)** 了解代码组织
4. 遵循 **[代码标准](./code-standards.md)** 进行开发

### 开发过程参考
- 开发前：查看 **[开发工作流](./development-workflow.md)**
- 编码时：参考 **[代码标准](./code-standards.md)** 和 **[组件架构](./component-architecture.md)**
- 测试时：遵循 **[测试规范](./testing-procedures.md)**
- 发布前：执行 **[部署发布](./deployment-process.md)** 流程

### 特定功能开发
- UI开发：参考 **[UI设计规范](./ui-guidelines.md)**
- 数据处理：查看 **[数据管理](./data-management.md)**
- 网络功能：参考 **[网络通信](./network-architecture.md)**
- 性能优化：查看 **[小程序优化](./miniprogram-optimization.md)**

## 🔄 文档维护

### 更新原则
- 所有规范变更需要团队讨论确认
- 重大架构调整需要更新相关文档
- 新功能开发需要补充对应规范

### 版本控制
- 文档变更需要通过Git进行版本控制
- 重要变更需要在commit message中说明
- 定期review文档内容的时效性

---

**最后更新**: 2025年7月22日  
**文档版本**: v1.0  
**适用项目**: Cocos Creator 东方捉宠放置游戏

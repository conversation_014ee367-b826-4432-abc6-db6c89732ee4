# 🔧 东方妖怪捉宠放置游戏技术文档

> 📅 **更新时间**: 2025-01-08  
> 👤 **技术负责**: [开发者姓名]  
> 🎯 **版本**: v1.0

## 🏗️ 技术架构概览

### 🛠️ **技术栈**
```
技术架构
├── 前端 (游戏客户端)
│   ├── Cocos Creator 3.8.6
│   ├── TypeScript
│   ├── 微信小程序 SDK
│   └── 抖音小程序 SDK
├── 后端 (游戏服务器)
│   ├── Node.js + Express
│   ├── TypeScript
│   ├── Socket.io (实时通信)
│   └── JWT (身份验证)
└── 数据层
    ├── MongoDB (主数据库)
    ├── Redis (缓存)
    └── 云存储 (资源文件)
```

### 🎮 **客户端架构**

#### **核心模块设计**
```
客户端模块
├── 🎯 GameManager - 游戏主控制器
├── 🐾 PetSystem - 妖怪系统管理
├── ⏱️ ActionBarSystem - 行动条系统
├── ⚔️ BattleSystem - 战斗系统
├── 🏭 ProductionSystem - 生产系统
├── 🎯 CaptureSystem - 捕捉系统
├── 💤 IdleSystem - 挂机系统
├── 📱 UIManager - 界面管理
└── 🌐 NetworkManager - 网络通信
```

#### **数据管理架构**
```typescript
// 数据管理器基类
abstract class DataManager<T> {
  protected data: Map<string, T> = new Map();
  
  abstract load(): Promise<void>;
  abstract save(): Promise<void>;
  abstract validate(data: T): boolean;
  
  get(id: string): T | undefined {
    return this.data.get(id);
  }
  
  set(id: string, value: T): void {
    if (this.validate(value)) {
      this.data.set(id, value);
      this.markDirty(id);
    }
  }
  
  protected markDirty(id: string): void {
    // 标记数据需要同步
  }
}
```

## 🐾 妖怪系统技术实现

### 📊 **数据结构设计**
```typescript
// 妖怪基础数据
interface PetData {
  id: string;
  templateId: string;
  name: string;
  level: number;
  exp: number;
  rarity: PetRarity;
  attributes: PetAttributes;
  skills: PetSkill[];
  evolution: EvolutionData;
  status: PetStatus;
  createdAt: number;
  updatedAt: number;
}

// 妖怪属性
interface PetAttributes {
  hp: number;
  attack: number;
  defense: number;
  speed: number;
  intelligence: number;
  luck: number;
}

// 妖怪技能
interface PetSkill {
  id: string;
  level: number;
  exp: number;
  cooldown: number;
  lastUsed: number;
}
```

### 🎯 **捕捉系统实现**
```typescript
class CaptureSystem {
  private presets: Map<string, CapturePreset> = new Map();
  
  // 检查是否触发自动捕捉
  checkAutoCapture(monster: Monster, team: Team): CaptureAction | null {
    for (const member of team.members) {
      const preset = this.presets.get(member.id);
      if (!preset || !preset.isActive) continue;
      
      const target = preset.targets.find(t => t.monsterId === monster.templateId);
      if (!target) continue;
      
      if (this.evaluateConditions(monster, preset.conditions)) {
        return {
          playerId: member.id,
          monsterId: monster.id,
          skills: preset.skills,
          items: preset.items,
          priority: target.priority
        };
      }
    }
    return null;
  }
  
  // 执行捕捉操作
  async executeCapture(action: CaptureAction): Promise<CaptureResult> {
    // 1. 消耗技能和道具
    await this.consumeResources(action);
    
    // 2. 计算成功率
    const successRate = this.calculateSuccessRate(action);
    
    // 3. 执行捕捉判定
    const success = Math.random() < successRate;
    
    // 4. 处理结果
    if (success) {
      const pet = await this.createPetFromMonster(action.monsterId);
      await this.addPetToPlayer(action.playerId, pet);
    }
    
    return { success, successRate, pet: success ? pet : null };
  }
}
```

## ⏱️ 行动条系统技术实现

### 🔄 **时间管理核心**
```typescript
class ActionBarSystem {
  private actionQueues: Map<string, ActionQueue> = new Map();
  private globalTimer: number = 0;
  
  // 添加行动到队列
  addAction(playerId: string, action: GameAction): void {
    const queue = this.getOrCreateQueue(playerId);
    
    // 计算行动完成时间
    const completionTime = this.globalTimer + action.duration;
    action.completionTime = completionTime;
    
    if (action.type === ActionType.PARALLEL) {
      queue.parallelActions.push(action);
    } else {
      queue.serialActions.push(action);
    }
    
    this.sortQueues(queue);
  }
  
  // 更新行动条系统
  update(deltaTime: number): void {
    this.globalTimer += deltaTime;
    
    for (const [playerId, queue] of this.actionQueues) {
      this.processQueue(playerId, queue);
    }
  }
  
  // 处理队列中的行动
  private processQueue(playerId: string, queue: ActionQueue): void {
    // 处理并行行动
    queue.parallelActions = queue.parallelActions.filter(action => {
      if (this.globalTimer >= action.completionTime) {
        this.executeAction(playerId, action);
        return false; // 移除已完成的行动
      }
      return true;
    });
    
    // 处理串行行动
    if (queue.serialActions.length > 0) {
      const currentAction = queue.serialActions[0];
      if (this.globalTimer >= currentAction.completionTime) {
        this.executeAction(playerId, currentAction);
        queue.serialActions.shift();
      }
    }
  }
}
```

### 🎯 **行动类型定义**
```typescript
enum ActionType {
  SERIAL = 'serial',     // 串行行动
  PARALLEL = 'parallel'  // 并行行动
}

interface GameAction {
  id: string;
  type: ActionType;
  category: ActionCategory;
  duration: number;
  completionTime?: number;
  data: any;
  callback?: (result: any) => void;
}

enum ActionCategory {
  BATTLE = 'battle',
  PRODUCTION = 'production',
  CAPTURE = 'capture',
  PET_TRAINING = 'pet_training',
  EVOLUTION = 'evolution'
}
```

## 🏭 生产系统技术实现

### 🔨 **生产管理器**
```typescript
class ProductionSystem {
  private productionLines: Map<string, ProductionLine> = new Map();
  
  // 开始生产
  startProduction(playerId: string, recipe: Recipe, petAssist?: string): void {
    const line = this.getAvailableLine(playerId);
    if (!line) throw new Error('No available production line');
    
    const production: ProductionTask = {
      id: generateId(),
      playerId,
      recipeId: recipe.id,
      startTime: Date.now(),
      duration: this.calculateDuration(recipe, petAssist),
      petAssistId: petAssist,
      status: ProductionStatus.IN_PROGRESS
    };
    
    line.currentTask = production;
    
    // 添加到行动条系统
    const action: GameAction = {
      id: production.id,
      type: ActionType.PARALLEL,
      category: ActionCategory.PRODUCTION,
      duration: production.duration,
      data: production
    };
    
    this.actionBarSystem.addAction(playerId, action);
  }
  
  // 计算生产时间（考虑妖怪协助）
  private calculateDuration(recipe: Recipe, petAssistId?: string): number {
    let duration = recipe.baseDuration;
    
    if (petAssistId) {
      const pet = this.petSystem.getPet(petAssistId);
      if (pet && pet.skills.some(s => s.id === 'production_boost')) {
        duration *= 0.8; // 妖怪协助减少20%时间
      }
    }
    
    return duration;
  }
}
```

## 💤 挂机系统技术实现

### 🌙 **离线收益计算**
```typescript
class IdleSystem {
  // 计算离线收益
  calculateOfflineRewards(playerId: string, offlineTime: number): OfflineRewards {
    const player = this.playerSystem.getPlayer(playerId);
    const rewards: OfflineRewards = {
      exp: 0,
      gold: 0,
      items: [],
      petExp: new Map()
    };
    
    // 计算战斗收益
    const battleRewards = this.calculateBattleRewards(player, offlineTime);
    rewards.exp += battleRewards.exp;
    rewards.gold += battleRewards.gold;
    
    // 计算生产收益
    const productionRewards = this.calculateProductionRewards(player, offlineTime);
    rewards.items.push(...productionRewards.items);
    rewards.gold += productionRewards.gold;
    
    // 计算妖怪经验收益
    for (const pet of player.pets) {
      const petExpGain = this.calculatePetOfflineExp(pet, offlineTime);
      rewards.petExp.set(pet.id, petExpGain);
    }
    
    // 应用离线收益上限
    return this.applyOfflineLimits(rewards, offlineTime);
  }
  
  // 应用离线收益上限
  private applyOfflineLimits(rewards: OfflineRewards, offlineTime: number): OfflineRewards {
    const maxOfflineHours = 12; // 最大离线收益时间
    const effectiveTime = Math.min(offlineTime, maxOfflineHours * 3600);
    const ratio = effectiveTime / offlineTime;
    
    return {
      exp: Math.floor(rewards.exp * ratio),
      gold: Math.floor(rewards.gold * ratio),
      items: rewards.items.slice(0, Math.floor(rewards.items.length * ratio)),
      petExp: new Map([...rewards.petExp].map(([id, exp]) => [id, Math.floor(exp * ratio)]))
    };
  }
}
```

## 🌐 网络通信架构

### 📡 **实时通信**
```typescript
class NetworkManager {
  private socket: SocketIOClient.Socket;
  private messageQueue: NetworkMessage[] = [];
  
  // 发送消息
  send(type: MessageType, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const message: NetworkMessage = {
        id: generateId(),
        type,
        data,
        timestamp: Date.now(),
        resolve,
        reject
      };
      
      if (this.socket.connected) {
        this.socket.emit(type, message);
      } else {
        this.messageQueue.push(message);
      }
      
      // 设置超时
      setTimeout(() => {
        reject(new Error('Network timeout'));
      }, 10000);
    });
  }
  
  // 处理服务器消息
  private handleMessage(type: MessageType, data: any): void {
    switch (type) {
      case MessageType.BATTLE_RESULT:
        this.battleSystem.handleBattleResult(data);
        break;
      case MessageType.CAPTURE_RESULT:
        this.captureSystem.handleCaptureResult(data);
        break;
      case MessageType.PRODUCTION_COMPLETE:
        this.productionSystem.handleProductionComplete(data);
        break;
    }
  }
}
```

## 📊 性能优化策略

### 🚀 **客户端优化**
- **对象池管理** - 复用游戏对象，减少GC压力
- **资源预加载** - 关键资源提前加载
- **纹理压缩** - 使用适合的纹理格式
- **批量渲染** - 合并相似的渲染调用

### 🔧 **服务端优化**
- **数据库索引** - 优化查询性能
- **缓存策略** - Redis缓存热点数据
- **连接池** - 数据库连接复用
- **负载均衡** - 分布式部署

## 🛡️ 安全性设计

### 🔒 **数据安全**
- **输入验证** - 所有客户端输入都需要验证
- **权限检查** - 操作前检查用户权限
- **数据加密** - 敏感数据加密存储
- **防作弊机制** - 服务端验证关键逻辑

### 🚫 **反作弊系统**
```typescript
class AntiCheatSystem {
  // 验证行动时间
  validateActionTiming(playerId: string, action: GameAction): boolean {
    const lastAction = this.getLastAction(playerId);
    const minInterval = this.getMinActionInterval(action.category);
    
    return Date.now() - lastAction.timestamp >= minInterval;
  }
  
  // 验证资源变化
  validateResourceChange(playerId: string, change: ResourceChange): boolean {
    const player = this.getPlayer(playerId);
    const maxPossibleGain = this.calculateMaxGain(player, change.type);
    
    return change.amount <= maxPossibleGain;
  }
}
```

---

*🔧 技术架构的每一个细节都为了给玩家带来最佳的游戏体验！*

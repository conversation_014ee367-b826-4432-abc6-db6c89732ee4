/**
 * 网格列表管理器
 * 管理网格布局的物品列表，支持动态添加/删除物品
 */

import { _decorator, Component, Node, Layout, Prefab, instantiate } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('GridListManager')
export class GridListManager extends Component {
    
    @property({ tooltip: '每行显示的物品数量' })
    public itemsPerRow: number = 6;
    
    @property({ tooltip: '物品槽预制体' })
    public itemSlotPrefab: Prefab | null = null;
    
    @property({ tooltip: '物品槽大小' })
    public slotSize: { width: number; height: number } = { width: 80, height: 80 };
    
    @property({ tooltip: '物品间距' })
    public spacing: { x: number; y: number } = { x: 10, y: 10 };
    
    // 组件引用
    private layout: Layout | null = null;
    private itemSlots: Node[] = [];

    start() {
        this.initializeLayout();
        console.log('📋 网格列表管理器初始化完成');
    }

    /**
     * 初始化布局
     */
    private initializeLayout(): void {
        // 获取Layout组件
        this.layout = this.node.getComponent(Layout);
        if (!this.layout) {
            console.error('❌ 节点上没有Layout组件');
            return;
        }
        
        // 配置网格布局
        this.layout.type = Layout.Type.GRID;
        this.layout.resizeMode = Layout.ResizeMode.CONTAINER;
        this.layout.cellSize.set(this.slotSize.width, this.slotSize.height);
        this.layout.spacingX = this.spacing.x;
        this.layout.spacingY = this.spacing.y;
        this.layout.paddingLeft = 10;
        this.layout.paddingRight = 10;
        this.layout.paddingTop = 10;
        this.layout.paddingBottom = 10;
        
        // 设置网格约束（每行显示的数量）
        this.layout.constraint = this.itemsPerRow;
        this.layout.constraintNum = this.itemsPerRow;
        
        console.log(`📋 网格布局配置完成: ${this.itemsPerRow}列`);
    }

    /**
     * 添加物品槽
     */
    public addItemSlot(itemData?: any): Node | null {
        let slotNode: Node;
        
        if (this.itemSlotPrefab) {
            // 使用预制体创建
            slotNode = instantiate(this.itemSlotPrefab);
        } else {
            // 创建简单的槽位节点
            slotNode = this.createSimpleSlot();
        }
        
        slotNode.setParent(this.node);
        this.itemSlots.push(slotNode);
        
        // 绑定数据
        if (itemData) {
            this.bindItemData(slotNode, itemData);
        }
        
        // 更新布局
        this.updateLayout();
        
        console.log(`📋 添加物品槽，当前总数: ${this.itemSlots.length}`);
        return slotNode;
    }

    /**
     * 创建简单的槽位节点
     */
    private createSimpleSlot(): Node {
        const slotNode = new Node('ItemSlot');
        
        // 添加UITransform组件
        const transform = slotNode.addComponent('UITransform');
        transform.setContentSize(this.slotSize.width, this.slotSize.height);
        
        // 可以在这里添加Sprite组件作为背景
        // const sprite = slotNode.addComponent(Sprite);
        // sprite.color = new Color(100, 100, 100, 255);
        
        return slotNode;
    }

    /**
     * 绑定物品数据到槽位
     */
    private bindItemData(slotNode: Node, itemData: any): void {
        // 这里可以根据itemData设置槽位的显示
        // 例如设置图标、数量、品质等
        slotNode.name = `ItemSlot_${itemData.id || this.itemSlots.length}`;
    }

    /**
     * 移除物品槽
     */
    public removeItemSlot(index: number): void {
        if (index < 0 || index >= this.itemSlots.length) {
            console.warn(`❌ 无效的槽位索引: ${index}`);
            return;
        }
        
        const slotNode = this.itemSlots[index];
        slotNode.destroy();
        this.itemSlots.splice(index, 1);
        
        this.updateLayout();
        
        console.log(`📋 移除物品槽 ${index}，当前总数: ${this.itemSlots.length}`);
    }

    /**
     * 清空所有物品槽
     */
    public clearAllSlots(): void {
        this.itemSlots.forEach(slot => {
            if (slot && slot.isValid) {
                slot.destroy();
            }
        });
        
        this.itemSlots = [];
        this.updateLayout();
        
        console.log('📋 清空所有物品槽');
    }

    /**
     * 批量添加物品
     */
    public addItems(itemsData: any[]): void {
        itemsData.forEach(itemData => {
            this.addItemSlot(itemData);
        });
        
        console.log(`📋 批量添加 ${itemsData.length} 个物品`);
    }

    /**
     * 更新布局
     */
    private updateLayout(): void {
        if (this.layout) {
            // 强制更新布局
            this.layout.updateLayout();
            
            // 计算并更新容器大小
            this.updateContainerSize();
        }
    }

    /**
     * 更新容器大小
     */
    private updateContainerSize(): void {
        if (!this.layout) return;
        
        const totalItems = this.itemSlots.length;
        const rows = Math.ceil(totalItems / this.itemsPerRow);
        
        // 计算所需的高度
        const totalHeight = rows * this.slotSize.height + 
                           (rows - 1) * this.spacing.y + 
                           this.layout.paddingTop + 
                           this.layout.paddingBottom;
        
        // 计算所需的宽度
        const totalWidth = this.itemsPerRow * this.slotSize.width + 
                          (this.itemsPerRow - 1) * this.spacing.x + 
                          this.layout.paddingLeft + 
                          this.layout.paddingRight;
        
        // 更新节点大小
        const transform = this.node.getComponent('UITransform');
        if (transform) {
            transform.setContentSize(totalWidth, totalHeight);
        }
        
        console.log(`📋 容器大小更新: ${totalWidth} x ${totalHeight}, 行数: ${rows}`);
    }

    /**
     * 设置每行物品数量
     */
    public setItemsPerRow(count: number): void {
        this.itemsPerRow = count;
        
        if (this.layout) {
            this.layout.constraint = count;
            this.layout.constraintNum = count;
            this.updateLayout();
        }
        
        console.log(`📋 设置每行物品数量: ${count}`);
    }

    /**
     * 设置物品槽大小
     */
    public setSlotSize(width: number, height: number): void {
        this.slotSize.width = width;
        this.slotSize.height = height;
        
        if (this.layout) {
            this.layout.cellSize.set(width, height);
            this.updateLayout();
        }
        
        console.log(`📋 设置物品槽大小: ${width} x ${height}`);
    }

    /**
     * 设置间距
     */
    public setSpacing(x: number, y: number): void {
        this.spacing.x = x;
        this.spacing.y = y;
        
        if (this.layout) {
            this.layout.spacingX = x;
            this.layout.spacingY = y;
            this.updateLayout();
        }
        
        console.log(`📋 设置间距: ${x}, ${y}`);
    }

    /**
     * 获取物品槽数量
     */
    public getSlotCount(): number {
        return this.itemSlots.length;
    }

    /**
     * 获取指定索引的物品槽
     */
    public getSlot(index: number): Node | null {
        if (index < 0 || index >= this.itemSlots.length) {
            return null;
        }
        return this.itemSlots[index];
    }

    /**
     * 获取所有物品槽
     */
    public getAllSlots(): Node[] {
        return [...this.itemSlots];
    }
}

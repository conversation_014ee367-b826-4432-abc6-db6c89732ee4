/**
 * UI面板基础类
 * 所有UI面板的基础类，提供通用的面板功能
 */

import { _decorator, Component, Node, tween, Vec3, UIOpacity } from 'cc';
import { 
    IUIPanel, 
    IUIPanelConfig, 
    UIPanelType, 
    UIPanelState, 
    UIAnimationType 
} from '../types/UITypes';

const { ccclass, property } = _decorator;

@ccclass('BaseUIPanel')
export abstract class BaseUIPanel extends Component implements IUIPanel {
    
    @property({ tooltip: '面板类型' })
    protected _panelType: UIPanelType = UIPanelType.Dialog;
    
    @property({ tooltip: '是否启用显示动画' })
    protected _enableShowAnimation: boolean = true;
    
    @property({ tooltip: '是否启用隐藏动画' })
    protected _enableHideAnimation: boolean = true;
    
    @property({ tooltip: '动画持续时间' })
    protected _animationDuration: number = 0.3;
    
    // 面板状态
    protected _state: UIPanelState = UIPanelState.Hidden;
    protected _config: IUIPanelConfig | null = null;
    protected _initData: any = null;
    
    // 动画相关
    protected _originalScale: Vec3 = new Vec3();
    protected _originalPosition: Vec3 = new Vec3();
    
    /**
     * 面板类型
     */
    public get panelType(): UIPanelType {
        return this._panelType;
    }
    
    /**
     * 面板配置
     */
    public get config(): IUIPanelConfig {
        return this._config!;
    }
    
    /**
     * 当前状态
     */
    public get state(): UIPanelState {
        return this._state;
    }

    /**
     * 组件加载时调用
     */
    protected onLoad(): void {
        // 保存原始变换信息
        this._originalScale.set(this.node.scale);
        this._originalPosition.set(this.node.position);
        
        // 初始化面板
        this.onPanelLoad();
    }

    /**
     * 组件启用时调用
     */
    protected onEnable(): void {
        this.onPanelEnable();
    }

    /**
     * 组件禁用时调用
     */
    protected onDisable(): void {
        this.onPanelDisable();
    }

    /**
     * 组件销毁时调用
     */
    protected onDestroy(): void {
        console.log(`🗑️ BaseUIPanel onDestroy: ${this._panelType}`);

        // 调用面板销毁处理
        this.onPanelDestroy();

        // 解绑事件
        this.unbindEvents();

        // 更新状态
        this._state = UIPanelState.Destroyed;

        // 调用父类的onDestroy
        super.onDestroy();
    }

    // ==================== IUIPanel接口实现 ====================

    /**
     * 初始化面板
     */
    public async initialize(data?: any): Promise<void> {
        console.log(`🎨 初始化面板: ${this._panelType}`);
        
        this._initData = data;
        this._state = UIPanelState.Hidden;
        
        // 初始化UI组件
        await this.initializeUI();
        
        // 绑定事件
        this.bindEvents();
        
        // 子类初始化
        await this.onInitialize(data);
        
        console.log(`✅ 面板初始化完成: ${this._panelType}`);
    }

    /**
     * 显示面板
     */
    public async show(data?: any): Promise<void> {
        if (this._state === UIPanelState.Visible || this._state === UIPanelState.Showing) {
            return;
        }
        
        console.log(`🎨 显示面板: ${this._panelType}`);
        this._state = UIPanelState.Showing;
        
        // 准备显示
        await this.onBeforeShow(data);
        
        // 执行显示动画
        if (this._enableShowAnimation && this._config?.showAnimation) {
            await this.playShowAnimation();
        }
        
        this._state = UIPanelState.Visible;
        
        // 显示完成
        await this.onAfterShow(data);
        
        console.log(`✅ 面板显示完成: ${this._panelType}`);
    }

    /**
     * 隐藏面板
     */
    public async hide(): Promise<void> {
        if (this._state === UIPanelState.Hidden || this._state === UIPanelState.Hiding) {
            return;
        }
        
        console.log(`🎨 隐藏面板: ${this._panelType}`);
        this._state = UIPanelState.Hiding;
        
        // 准备隐藏
        await this.onBeforeHide();
        
        // 执行隐藏动画
        if (this._enableHideAnimation && this._config?.hideAnimation) {
            await this.playHideAnimation();
        }
        
        this._state = UIPanelState.Hidden;
        
        // 隐藏完成
        await this.onAfterHide();
        
        console.log(`✅ 面板隐藏完成: ${this._panelType}`);
    }

    /**
     * 销毁面板
     */
    public destroyPanel(): void {
        console.log(`🗑️ 销毁面板: ${this._panelType}`);

        this._state = UIPanelState.Destroyed;

        // 解绑事件
        this.unbindEvents();

        // 子类销毁处理
        this.onPanelDestroy();

        // 销毁节点
        if (this.node && this.node.isValid) {
            this.node.destroy();
        }
    }

    /**
     * 刷新面板
     */
    public refresh(data?: any): void {
        console.log(`🔄 刷新面板: ${this._panelType}`);
        this.onRefresh(data);
    }

    /**
     * 处理返回键
     */
    public onBackPressed(): boolean {
        // 默认行为：隐藏面板
        this.hide();
        return true;
    }

    /**
     * 处理ESC键
     */
    public onEscapePressed(): boolean {
        // 默认行为：如果配置允许，隐藏面板
        if (this._config?.escapeToClose) {
            this.hide();
            return true;
        }
        return false;
    }

    // ==================== 动画方法 ====================

    /**
     * 播放显示动画
     */
    protected async playShowAnimation(): Promise<void> {
        const animationType = this._config?.showAnimation || UIAnimationType.Fade;
        const duration = this._config?.animationDuration || this._animationDuration;
        
        return new Promise<void>((resolve) => {
            switch (animationType) {
                case UIAnimationType.Fade:
                    this.playFadeInAnimation(duration, resolve);
                    break;
                case UIAnimationType.Scale:
                    this.playScaleInAnimation(duration, resolve);
                    break;
                case UIAnimationType.Slide:
                    this.playSlideInAnimation(duration, resolve);
                    break;
                default:
                    resolve();
                    break;
            }
        });
    }

    /**
     * 播放隐藏动画
     */
    protected async playHideAnimation(): Promise<void> {
        const animationType = this._config?.hideAnimation || UIAnimationType.Fade;
        const duration = this._config?.animationDuration || this._animationDuration;
        
        return new Promise<void>((resolve) => {
            switch (animationType) {
                case UIAnimationType.Fade:
                    this.playFadeOutAnimation(duration, resolve);
                    break;
                case UIAnimationType.Scale:
                    this.playScaleOutAnimation(duration, resolve);
                    break;
                case UIAnimationType.Slide:
                    this.playSlideOutAnimation(duration, resolve);
                    break;
                default:
                    resolve();
                    break;
            }
        });
    }

    /**
     * 淡入动画
     */
    private playFadeInAnimation(duration: number, callback: () => void): void {
        const opacity = this.node.getComponent(UIOpacity) || this.node.addComponent(UIOpacity);
        opacity.opacity = 0;
        
        tween(opacity)
            .to(duration, { opacity: 255 })
            .call(callback)
            .start();
    }

    /**
     * 淡出动画
     */
    private playFadeOutAnimation(duration: number, callback: () => void): void {
        const opacity = this.node.getComponent(UIOpacity) || this.node.addComponent(UIOpacity);
        
        tween(opacity)
            .to(duration, { opacity: 0 })
            .call(callback)
            .start();
    }

    /**
     * 缩放进入动画
     */
    private playScaleInAnimation(duration: number, callback: () => void): void {
        this.node.setScale(0, 0, 1);
        
        tween(this.node)
            .to(duration, { scale: this._originalScale }, { easing: 'backOut' })
            .call(callback)
            .start();
    }

    /**
     * 缩放退出动画
     */
    private playScaleOutAnimation(duration: number, callback: () => void): void {
        tween(this.node)
            .to(duration, { scale: new Vec3(0, 0, 1) }, { easing: 'backIn' })
            .call(callback)
            .start();
    }

    /**
     * 滑入动画
     */
    private playSlideInAnimation(duration: number, callback: () => void): void {
        const startPos = new Vec3(this._originalPosition.x, this._originalPosition.y + 1000, this._originalPosition.z);
        this.node.setPosition(startPos);
        
        tween(this.node)
            .to(duration, { position: this._originalPosition }, { easing: 'quartOut' })
            .call(callback)
            .start();
    }

    /**
     * 滑出动画
     */
    private playSlideOutAnimation(duration: number, callback: () => void): void {
        const endPos = new Vec3(this._originalPosition.x, this._originalPosition.y - 1000, this._originalPosition.z);
        
        tween(this.node)
            .to(duration, { position: endPos }, { easing: 'quartIn' })
            .call(callback)
            .start();
    }

    // ==================== 抽象方法（子类实现） ====================

    /**
     * 面板加载时调用
     */
    protected abstract onPanelLoad(): void;

    /**
     * 面板启用时调用
     */
    protected abstract onPanelEnable(): void;

    /**
     * 面板禁用时调用
     */
    protected abstract onPanelDisable(): void;

    /**
     * 面板销毁时调用
     */
    protected abstract onPanelDestroy(): void;

    /**
     * 初始化UI组件
     */
    protected abstract initializeUI(): Promise<void>;

    /**
     * 绑定事件
     */
    protected abstract bindEvents(): void;

    /**
     * 解绑事件
     */
    protected abstract unbindEvents(): void;

    /**
     * 子类初始化
     */
    protected abstract onInitialize(data?: any): Promise<void>;

    /**
     * 显示前处理
     */
    protected abstract onBeforeShow(data?: any): Promise<void>;

    /**
     * 显示后处理
     */
    protected abstract onAfterShow(data?: any): Promise<void>;

    /**
     * 隐藏前处理
     */
    protected abstract onBeforeHide(): Promise<void>;

    /**
     * 隐藏后处理
     */
    protected abstract onAfterHide(): Promise<void>;

    /**
     * 刷新处理
     */
    protected abstract onRefresh(data?: any): void;
}

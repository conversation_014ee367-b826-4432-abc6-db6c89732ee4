/**
 * 实体数据接口定义
 * 基于原Godot项目的entities.xml结构
 */

/**
 * 基础属性接口
 */
export interface IBaseAttributes {
    /** 生命值 */
    health: number;
    
    /** 最大生命值 */
    maxHealth: number;
    
    /** 魔法值 */
    mana: number;
    
    /** 最大魔法值 */
    maxMana: number;
    
    /** 力量 */
    strength: number;
    
    /** 敏捷 */
    agility: number;
    
    /** 智力 */
    intelligence: number;
    
    /** 体质 */
    constitution: number;
    
    /** 精神 */
    spirit: number;
    
    /** 幸运 */
    luck: number;
}

/**
 * 战斗属性接口
 */
export interface ICombatAttributes {
    /** 攻击力 */
    attack: number;
    
    /** 防御力 */
    defense: number;
    
    /** 魔法攻击力 */
    magicAttack: number;
    
    /** 魔法防御力 */
    magicDefense: number;
    
    /** 命中率 */
    accuracy: number;
    
    /** 闪避率 */
    evasion: number;
    
    /** 暴击率 */
    criticalRate: number;
    
    /** 暴击伤害 */
    criticalDamage: number;
    
    /** 攻击速度 */
    attackSpeed: number;
    
    /** 移动速度 */
    moveSpeed: number;
}

/**
 * 位置信息接口
 */
export interface IPosition {
    /** X坐标 */
    x: number;
    
    /** Y坐标 */
    y: number;
    
    /** Z坐标 */
    z?: number;
    
    /** 朝向角度 */
    rotation?: number;
}

/**
 * 实体状态枚举
 */
export enum EntityState {
    IDLE = 'idle',
    MOVING = 'moving',
    ATTACKING = 'attacking',
    CASTING = 'casting',
    DEAD = 'dead',
    STUNNED = 'stunned',
    SLEEPING = 'sleeping'
}

/**
 * 实体类型枚举
 */
export enum EntityType {
    PLAYER = 'player',
    NPC = 'npc',
    MONSTER = 'monster',
    BOSS = 'boss',
    PET = 'pet',
    SUMMON = 'summon'
}

/**
 * 实体数据接口
 */
export interface IEntityData {
    /** 实体ID */
    id: string;
    
    /** 实体名称 */
    name: string;
    
    /** 实体类型 */
    type: EntityType;
    
    /** 等级 */
    level: number;
    
    /** 经验值 */
    experience: number;
    
    /** 升级所需经验 */
    experienceToNext: number;
    
    /** 基础属性 */
    baseAttributes: IBaseAttributes;
    
    /** 战斗属性 */
    combatAttributes: ICombatAttributes;
    
    /** 当前位置 */
    position: IPosition;
    
    /** 当前状态 */
    state: EntityState;
    
    /** 所属区域ID */
    areaId?: string;
    
    /** 模型路径 */
    modelPath?: string;
    
    /** 头像路径 */
    avatarPath?: string;
    
    /** 描述 */
    description?: string;
    
    /** 标签 */
    tags?: string[];
    
    /** 创建时间 */
    createdAt?: Date;
    
    /** 最后更新时间 */
    updatedAt?: Date;
    
    /** 最后登录时间 */
    lastLoginAt?: Date;
    
    /** 在线时长(秒) */
    onlineTime?: number;
    
    /** 是否在线 */
    isOnline?: boolean;
}

/**
 * 玩家数据接口
 */
export interface IPlayerData extends IEntityData {
    /** 用户ID */
    userId: string;
    
    /** 用户名 */
    username: string;
    
    /** 邮箱 */
    email?: string;
    
    /** 职业 */
    profession: string;
    
    /** 技能点 */
    skillPoints: number;
    
    /** 属性点 */
    attributePoints: number;
    
    /** 金币 */
    gold: number;
    
    /** 钻石 */
    diamonds: number;
    
    /** 声望 */
    reputation: number;
    
    /** 公会ID */
    guildId?: string;
    
    /** 好友列表 */
    friends?: string[];
    
    /** 黑名单 */
    blacklist?: string[];
    
    /** 成就列表 */
    achievements?: string[];
    
    /** 称号 */
    titles?: string[];
    
    /** 当前称号 */
    currentTitle?: string;
    
    /** VIP等级 */
    vipLevel?: number;
    
    /** VIP经验 */
    vipExperience?: number;
}

/**
 * NPC数据接口
 */
export interface INPCData extends IEntityData {
    /** NPC类型 */
    npcType: 'merchant' | 'quest_giver' | 'trainer' | 'guard' | 'other';
    
    /** 对话内容 */
    dialogues?: string[];
    
    /** 提供的任务 */
    quests?: string[];
    
    /** 出售的物品 */
    shopItems?: string[];
    
    /** 可训练的技能 */
    trainableSkills?: string[];
    
    /** 巡逻路径 */
    patrolPath?: IPosition[];
    
    /** 是否友好 */
    isFriendly: boolean;
}

/**
 * 怪物数据接口
 */
export interface IMonsterData extends IEntityData {
    /** 怪物种族 */
    race: string;
    
    /** 怪物等级 */
    monsterLevel: number;
    
    /** 掉落物品 */
    dropItems?: string[];
    
    /** 掉落概率 */
    dropRates?: number[];
    
    /** 经验值奖励 */
    experienceReward: number;
    
    /** 金币奖励 */
    goldReward: number;
    
    /** 攻击模式 */
    attackPattern?: string;
    
    /** 技能列表 */
    skills?: string[];
    
    /** 重生时间(秒) */
    respawnTime?: number;
    
    /** 重生位置 */
    respawnPosition?: IPosition;
    
    /** 是否为Boss */
    isBoss: boolean;
    
    /** 仇恨值 */
    aggro?: number;
    
    /** 追击距离 */
    chaseDistance?: number;
    
    /** 警戒距离 */
    alertDistance?: number;
}

/**
 * 实体配置接口
 */
export interface IEntityConfig {
    /** 实体列表 */
    entities: IEntityData[];
    
    /** 玩家配置 */
    players?: IPlayerData[];
    
    /** NPC配置 */
    npcs?: INPCData[];
    
    /** 怪物配置 */
    monsters?: IMonsterData[];
    
    /** 全局设置 */
    globalSettings?: {
        /** 基础经验值倍数 */
        baseExperienceMultiplier: number;
        
        /** 基础金币倍数 */
        baseGoldMultiplier: number;
        
        /** 最大等级 */
        maxLevel: number;
        
        /** 属性成长系数 */
        attributeGrowthRate: number;
    };
}

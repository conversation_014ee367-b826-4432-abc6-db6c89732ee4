# 🐾 东方妖怪捉宠放置游戏设计文档

> 📅 **更新时间**: 2025-01-08  
> 👤 **策划**: 张睿哲  
> 🎯 **版本**: v1.0

## 🎮 游戏概述

### 🌟 **核心理念**
东方妖怪捉宠放置游戏是一款融合了**生产经营**、**战斗冒险**、**宠物养成**的挂机放置游戏。玩家在古风仙侠的东方妖怪世界中，通过智能的行动条系统管理时间，捕捉和培养各种可爱的妖怪伙伴，打造属于自己的妖怪农场。

### 🎯 **设计目标**
- **深度挂机体验**：真正的离线收益，自动化管理
- **策略性玩法**：行动条系统带来的时间管理策略
- **收集乐趣**：丰富的妖怪收集和养成系统
- **社交互动**：组队合作，妖怪交易

## 🏮 世界观设定

### 🌸 **背景故事**
在遥远的东方仙境中，人类与妖怪和谐共存。玩家作为一名年轻的妖怪契约师，踏上了收集和培养妖怪伙伴的冒险之旅。通过与妖怪建立契约，不仅能在战斗中获得强大的助力，还能让它们协助进行各种生产活动。

### 🗺️ **世界地图**
```
东方妖怪世界
├── 🌸 樱花村 - 新手村庄，基础妖怪栖息地
├── 🌲 竹林秘境 - 植物系妖怪的家园
├── 🏔️ 雪山仙境 - 冰系妖怪的栖息地
├── 🔥 火焰山谷 - 火系妖怪的领域
├── 🌊 碧波湖泊 - 水系妖怪的乐园
├── ⚡ 雷鸣峡谷 - 雷系妖怪的巢穴
└── 🌙 月影森林 - 稀有妖怪的神秘之地
```

## ⚔️ 核心系统设计

### 🐾 **妖怪系统**

#### **妖怪分类**
```
妖怪属性分类
├── 🌿 木系 - 擅长生产和治疗
├── 🔥 火系 - 高攻击力，擅长战斗
├── 🌊 水系 - 平衡型，适应性强
├── ⚡ 雷系 - 高速度，爆发力强
├── 🏔️ 冰系 - 控制型，擅长辅助
└── 🌙 暗系 - 稀有属性，特殊能力
```

#### **稀有度等级**
- **🤍 普通** - 基础妖怪，容易获得
- **💚 优秀** - 属性较好，中等稀有度
- **💙 稀有** - 属性优秀，较难获得
- **💜 史诗** - 属性强大，非常稀有
- **🧡 传说** - 最强妖怪，极其稀有

#### **妖怪功能**
1. **战斗伙伴** - 与玩家协同作战
2. **生产助手** - 协助各种生产活动
3. **收集要素** - 图鉴收集，成就系统

### ⏱️ **行动条系统**

#### **核心机制**
```
行动条架构
├── 定位系统
│   ├── 体力管理 - 限制行动频率
│   └── 时间成本 - 每个行动都需要时间
└── 功能系统
    ├── 行动条 - 显示预计完成时间
    └── 队列管理
        ├── 串行队列 - 按顺序执行
        └── 并行队列 - 同时进行多个任务
```

#### **应用场景**
- **战斗行动** - 攻击、技能、道具使用
- **生产行动** - 采集、制作、炼化
- **妖怪行动** - 训练、进化、繁殖
- **捕捉行动** - 妖怪捕捉过程

### 🎯 **捕捉系统**

#### **捕捉流程**
```
捕捉机制
├── 战斗阶段 - 自动战斗击败妖怪
├── 捕捉环节 - 战斗胜利后的捕捉机会
├── 预设系统 - 智能捕捉配置
└── 成功判定 - 基于多种因素的成功率
```

#### **预设捕捉系统**
```
预设配置
├── 目标妖怪 - 设定想要捕捉的妖怪类型
├── 捕捉技能 - 配置辅助捕捉的技能
├── 捕捉道具 - 设定使用的道具类型
├── 触发条件 - 自动捕捉的触发条件
└── 优先级 - 多目标时的优先级排序
```

### 🏭 **生产系统**

#### **生产类型**
```
生产活动
├── 🌿 采集系统
│   ├── 采药 - 收集各种草药
│   ├── 挖矿 - 获取矿物资源
│   └── 捕鱼 - 捕捞水产资源
├── 🔨 制作系统
│   ├── 炼药 - 制作恢复和增益药品
│   ├── 锻造 - 制作武器和装备
│   └── 烹饪 - 制作食物和妖怪饲料
└── 🐾 妖怪协助
    ├── 生产加速 - 妖怪协助提高效率
    ├── 品质提升 - 特定妖怪提高产品品质
    └── 自动生产 - 妖怪独立完成生产任务
```

### ⚔️ **战斗系统**

#### **战斗模式**
- **协同战斗** - 玩家角色 + 妖怪伙伴
- **自动战斗** - 基于AI的自动战斗
- **策略配置** - 预设战斗策略和技能使用

#### **战斗机制**
```
战斗流程
├── 战前准备 - 选择妖怪伙伴，配置技能
├── 行动排序 - 基于速度属性的行动顺序
├── 技能释放 - 玩家和妖怪的技能配合
├── 伤害计算 - 属性克制和技能效果
└── 胜负判定 - 进入捕捉环节或战斗结束
```

## 💤 挂机系统设计

### 🌙 **离线收益**
- **生产收益** - 离线时继续进行生产活动
- **战斗收益** - 自动战斗获得经验和资源
- **妖怪成长** - 妖怪离线时也会获得经验
- **收益上限** - 防止无限制离线收益

### 🤖 **自动化系统**
- **智能战斗** - 根据预设策略自动战斗
- **自动捕捉** - 基于预设配置自动捕捉妖怪
- **生产管理** - 自动安排生产任务
- **资源管理** - 智能分配和使用资源

## 🎨 UI/UX 设计原则

### 📱 **界面设计**
- **古风美学** - 东方仙侠风格的视觉设计
- **简洁明了** - 信息层次清晰，操作简单
- **挂机友好** - 重要信息一目了然
- **移动优化** - 适配小程序平台特性

### 🎮 **交互设计**
- **一键操作** - 复杂操作简化为一键完成
- **智能提示** - 及时的操作反馈和提示
- **个性化** - 支持个人偏好设置
- **社交元素** - 便捷的社交互动功能

## 📊 数值设计框架

### 🎯 **成长曲线**
- **玩家等级** - 线性成长，每级固定经验需求
- **妖怪等级** - 指数成长，高级妖怪成长更慢
- **技能等级** - 分段成长，每个阶段有质的提升

### 💰 **经济系统**
- **多元货币** - 金币、灵石、妖怪币等
- **资源循环** - 生产-消费-再生产的良性循环
- **价值平衡** - 时间投入与收益的合理比例

## 🚀 开发里程碑

### 📅 **开发计划**
- **Phase 1** - 核心系统框架 (4周)
- **Phase 2** - 妖怪系统实现 (6周)
- **Phase 3** - 生产系统完善 (4周)
- **Phase 4** - UI/UX优化 (3周)
- **Phase 5** - 测试和优化 (3周)

---

*🌸 在东方妖怪的奇幻世界中，每一次冒险都充满惊喜！*

import { _decorator, Component, Node, director, input, Input, EventKeyboard, KeyCode, game } from 'cc';
import { NetworkManager } from '../network/NetworkManager';
import { HttpClient } from '../network/HttpClient';
import { WebSocketClient } from '../network/WebSocketClient';
import { Network } from '../network/index';
import { NetworkEventType } from '../network/types/NetworkTypes';
import { EventManager } from '../managers/EventManager';
import { Managers } from '../managers/index';

const { ccclass, property } = _decorator;

/**
 * 战斗场景控制器
 * 负责武侠放置游戏的战斗逻辑和战斗UI管理
 */
@ccclass('BattleScene')
export class BattleScene extends Component {
    private _networkManager: NetworkManager;
    private _httpClient: HttpClient;
    private _websocketClient: WebSocketClient;
    private _testMode: 'scene' | 'network' | 'system' | 'performance' = 'scene';

    protected onLoad(): void {
        console.log('⚔️ 战斗场景加载');
        this.initializeNetworkComponents();
        this.initializeKeyboardInput();
        this.setupNetworkEventListeners();
    }

    protected start(): void {
        console.log('⚔️ 战斗场景开始');
        console.log('✅ 战斗场景运行正常');
        this.showTestInstructions();
    }

    /**
     * 撤退按钮点击事件
     */
    public onRetreatButtonClicked(): void {
        console.log('🏃 玩家选择撤退');
        this.handleRetreat();
    }

    /**
     * 处理撤退逻辑
     */
    private handleRetreat(): void {
        console.log('🔄 返回主场景');
        // 返回主场景
        director.loadScene('MainScene');
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        switch (this._testMode) {
            case 'scene':
                console.log('🎮 ========== 场景切换测试 ==========');
                console.log('📍 当前场景: Battle (战斗场景)');
                console.log('⌨️ 键盘快捷键:');
                console.log('   按 1 键 - 切换到 Launch 场景');
                console.log('   按 2 键 - 切换到 Main 场景');
                console.log('   按 3 键 - 切换到 Battle 场景');
                console.log('   按 N 键 - 切换到网络测试模式');
                console.log('   按 I 键 - 切换到系统集成测试模式');
                console.log('   按 P 键 - 切换到性能测试模式');
                console.log('   按 H 键 - 显示帮助信息');
                console.log('🎮 ===================================');
                break;
            case 'network':
                console.log('🌐 ========== 网络模块测试 ==========');
                console.log('📍 当前模式: Network (网络测试)');
                console.log('⌨️ 键盘快捷键:');
                console.log('   按 1 键 - 测试HTTP GET请求');
                console.log('   按 2 键 - 测试HTTP POST请求');
                console.log('   按 3 键 - 测试WebSocket连接');
                console.log('   按 4 键 - 发送WebSocket消息');
                console.log('   按 5 键 - 查看网络状态');
                console.log('   按 6 键 - 测试便捷API');
                console.log('   按 7 键 - 测试错误处理');
                console.log('   按 8 键 - 断开WebSocket');
                console.log('   按 S 键 - 切换到场景测试模式');
                console.log('   按 H 键 - 显示帮助信息');
                console.log('🌐 ===================================');
                break;
            case 'system':
                console.log('🧪 ========== 系统集成测试 ==========');
                console.log('📍 当前模式: System Integration (系统集成测试)');
                console.log('⌨️ 键盘快捷键:');
                console.log('   按 1 键 - 运行所有测试');
                console.log('   按 2 键 - 运行单个测试');
                console.log('   按 3 键 - 查看测试结果');
                console.log('   按 4 键 - 重置测试结果');
                console.log('   按 5 键 - 测试管理器状态');
                console.log('   按 S 键 - 切换到场景测试模式');
                console.log('   按 H 键 - 显示帮助信息');
                console.log('🧪 ===================================');
                break;
            case 'performance':
                console.log('📊 ========== 性能基准测试 ==========');
                console.log('📍 当前模式: Performance Benchmark (性能基准测试)');
                console.log('⌨️ 键盘快捷键:');
                console.log('   按 1 键 - 测试启动时间');
                console.log('   按 2 键 - 测试内存使用');
                console.log('   按 3 键 - 测试帧率稳定性');
                console.log('   按 4 键 - 开始/停止性能监控');
                console.log('   按 5 键 - 显示性能报告');
                console.log('   按 6 键 - 压力测试');
                console.log('   按 7 键 - 清理性能数据');
                console.log('   按 S 键 - 切换到场景测试模式');
                console.log('   按 H 键 - 显示帮助信息');
                console.log('📊 ===================================');
                break;
        }
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (this._testMode) {
            case 'scene':
                this.handleSceneKeys(event);
                break;
            case 'network':
                this.handleNetworkKeys(event);
                break;
            case 'system':
                this.handleSystemKeys(event);
                break;
            case 'performance':
                this.handlePerformanceKeys(event);
                break;
        }
    }

    /**
     * 处理场景模式按键
     */
    private handleSceneKeys(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.switchToLaunch();
                break;
            case KeyCode.DIGIT_2:
                this.switchToMain();
                break;
            case KeyCode.DIGIT_3:
                this.switchToBattle();
                break;
            case KeyCode.KEY_N:
                this.switchToNetworkMode();
                break;
            case KeyCode.KEY_I:
                this.switchToSystemMode();
                break;
            case KeyCode.KEY_P:
                this.switchToPerformanceMode();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 处理网络模式按键
     */
    private handleNetworkKeys(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.testHttpGet();
                break;
            case KeyCode.DIGIT_2:
                this.testHttpPost();
                break;
            case KeyCode.DIGIT_3:
                this.testWebSocketConnect();
                break;
            case KeyCode.DIGIT_4:
                this.testWebSocketSend();
                break;
            case KeyCode.DIGIT_5:
                this.testNetworkStatus();
                break;
            case KeyCode.DIGIT_6:
                this.testConvenienceAPI();
                break;
            case KeyCode.DIGIT_7:
                this.testErrorHandling();
                break;
            case KeyCode.DIGIT_8:
                this.testWebSocketDisconnect();
                break;
            case KeyCode.KEY_S:
                this.switchToSceneMode();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 切换到启动场景
     */
    private switchToLaunch(): void {
        console.log('🚀 切换到启动场景');
        this.switchScene('Launch');
    }

    /**
     * 切换到主界面场景
     */
    private switchToMain(): void {
        console.log('🏠 切换到主界面场景');
        this.switchScene('Main');
    }

    /**
     * 切换到战斗场景
     */
    private switchToBattle(): void {
        console.log('⚔️ 切换到战斗场景');
        this.switchScene('Battle');
    }

    /**
     * 通用场景切换方法
     */
    private switchScene(sceneName: string): void {
        try {
            console.log(`🔄 正在切换到场景: ${sceneName}`);
            director.loadScene(sceneName, (error) => {
                if (error) {
                    console.error(`❌ 场景切换失败: ${sceneName}`, error);
                } else {
                    console.log(`✅ 场景切换成功: ${sceneName}`);
                }
            });
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    /**
     * 测试方法 - 返回主界面
     */
    public testBackToMain(): void {
        console.log('🔙 测试返回主界面');
        director.loadScene('Main');
    }

    /**
     * 测试方法 - 返回启动场景
     */
    public testBackToLaunch(): void {
        console.log('🔙 测试返回启动场景');
        director.loadScene('Launch');
    }



    /**
     * 初始化网络组件
     */
    private initializeNetworkComponents(): void {
        this._networkManager = NetworkManager.getInstance();
        this._httpClient = new HttpClient({
            baseURL: 'https://jsonplaceholder.typicode.com',
            timeout: 5000,
            retries: 2
        });
        this._websocketClient = new WebSocketClient({
            reconnectInterval: 3000,
            maxReconnectAttempts: 3,
            heartbeatInterval: 30000
        });
    }

    /**
     * 设置网络事件监听
     */
    private setupNetworkEventListeners(): void {
        const eventManager = EventManager.getInstance();

        eventManager.on(NetworkEventType.ONLINE, () => {
            console.log('🟢 网络已连接');
        });

        eventManager.on(NetworkEventType.OFFLINE, () => {
            console.log('🔴 网络已断开');
        });

        eventManager.on(NetworkEventType.REQUEST_START, (data) => {
            console.log('📤 HTTP请求开始:', data.config.url);
        });

        eventManager.on(NetworkEventType.REQUEST_SUCCESS, (data) => {
            console.log('✅ HTTP请求成功:', data.response.status);
        });

        eventManager.on(NetworkEventType.REQUEST_ERROR, (data) => {
            console.log('❌ HTTP请求失败:', data.error.message);
        });

        eventManager.on(NetworkEventType.WEBSOCKET_CONNECT, () => {
            console.log('🔗 WebSocket连接成功');
        });

        eventManager.on(NetworkEventType.WEBSOCKET_DISCONNECT, () => {
            console.log('🔌 WebSocket连接断开');
        });

        eventManager.on(NetworkEventType.WEBSOCKET_MESSAGE, (data) => {
            console.log('📨 WebSocket消息:', data.message);
        });
    }

    /**
     * 切换到网络测试模式
     */
    private switchToNetworkMode(): void {
        this._testMode = 'network';
        console.log('🌐 切换到网络测试模式');
        this.showTestInstructions();
    }

    /**
     * 切换到场景测试模式
     */
    private switchToSceneMode(): void {
        this._testMode = 'scene';
        console.log('🎮 切换到场景测试模式');
        this.showTestInstructions();
    }

    /**
     * 切换到系统集成测试模式
     */
    private switchToSystemMode(): void {
        this._testMode = 'system';
        console.log('🧪 切换到系统集成测试模式');
        this.showTestInstructions();
    }

    /**
     * 切换到性能测试模式
     */
    private switchToPerformanceMode(): void {
        this._testMode = 'performance';
        console.log('📊 切换到性能测试模式');
        this.showTestInstructions();
    }

    /**
     * 测试HTTP GET请求
     */
    private async testHttpGet(): Promise<void> {
        console.log('🔄 测试HTTP GET请求...');

        try {
            const response = await this._httpClient.get('/posts/1');
            console.log('✅ HTTP GET成功:', response);
        } catch (error) {
            console.error('❌ HTTP GET失败:', error);
        }
    }

    /**
     * 测试HTTP POST请求
     */
    private async testHttpPost(): Promise<void> {
        console.log('🔄 测试HTTP POST请求...');

        try {
            const data = {
                title: '武侠放置游戏',
                body: '这是一个测试POST请求',
                userId: 1
            };

            const response = await this._httpClient.post('/posts', data);
            console.log('✅ HTTP POST成功:', response);
        } catch (error) {
            console.error('❌ HTTP POST失败:', error);
        }
    }

    /**
     * 测试WebSocket连接
     */
    private async testWebSocketConnect(): Promise<void> {
        console.log('🔄 测试WebSocket连接...');

        try {
            // 使用公共WebSocket测试服务器
            await this._websocketClient.connect('wss://echo.websocket.org');
            console.log('✅ WebSocket连接成功');

            // 设置消息监听
            this._websocketClient.on('message', (message) => {
                console.log('📨 收到WebSocket消息:', message);
            });

        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
        }
    }

    /**
     * 测试WebSocket发送消息
     */
    private testWebSocketSend(): void {
        console.log('🔄 测试WebSocket发送消息...');

        if (this._websocketClient.isConnected()) {
            // 发送简单的文本消息（适合echo服务器）
            const textMessage = `Hello from 武侠放置游戏! Time: ${new Date().toLocaleTimeString()}`;
            this._websocketClient.send(textMessage);
            console.log('📤 WebSocket文本消息已发送:', textMessage);

            // 也测试JSON格式的消息
            const jsonMessage = {
                type: 'game_message',
                data: {
                    player: '测试玩家',
                    action: '发送消息',
                    timestamp: Date.now()
                }
            };

            setTimeout(() => {
                this._websocketClient.send(jsonMessage);
                console.log('📤 WebSocket JSON消息已发送:', jsonMessage);
            }, 1000);
        } else {
            console.warn('⚠️ WebSocket未连接，请先连接');
        }
    }

    /**
     * 测试网络状态
     */
    private testNetworkStatus(): void {
        console.log('🔄 测试网络状态...');

        const status = this._networkManager.getNetworkStatus();
        console.log('📊 网络状态:', {
            在线状态: status.isOnline ? '在线' : '离线',
            网络类型: status.networkType,
            连接质量: status.connectionQuality,
            延迟: status.latency + 'ms',
            最后检查: new Date(status.lastCheck).toLocaleTimeString()
        });

        console.log('🔍 网络类型:', this._networkManager.getNetworkType());
        console.log('🌐 是否在线:', this._networkManager.isOnline());
    }

    /**
     * 测试便捷API
     */
    private async testConvenienceAPI(): Promise<void> {
        console.log('🔄 测试便捷API...');

        try {
            // 使用便捷API发送GET请求
            const response = await Network.get('https://jsonplaceholder.typicode.com/users/1');
            console.log('✅ 便捷API GET成功:', response);

            // 测试网络状态便捷API
            console.log('📊 便捷API网络状态:', Network.getNetworkStatus());
            console.log('🌐 便捷API在线状态:', Network.isOnline());

        } catch (error) {
            console.error('❌ 便捷API测试失败:', error);
        }
    }

    /**
     * 测试错误处理
     */
    private async testErrorHandling(): Promise<void> {
        console.log('🔄 测试错误处理...');

        try {
            // 故意发送一个会失败的请求
            await this._httpClient.get('/nonexistent-endpoint');
        } catch (error) {
            console.log('✅ 错误处理测试成功，捕获到预期错误:', error);
        }

        try {
            // 测试超时
            const timeoutClient = new HttpClient({ timeout: 1 });
            await timeoutClient.get('https://httpbin.org/delay/5');
        } catch (error) {
            console.log('✅ 超时处理测试成功:', error);
        }
    }

    /**
     * 测试WebSocket断开
     */
    private testWebSocketDisconnect(): void {
        console.log('🔄 测试WebSocket断开...');

        if (this._websocketClient.isConnected()) {
            this._websocketClient.disconnect();
            console.log('✅ WebSocket已断开');
        } else {
            console.warn('⚠️ WebSocket未连接');
        }
    }

    /**
     * 处理系统集成测试按键
     */
    private handleSystemKeys(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.runSystemIntegrationTest();
                break;
            case KeyCode.DIGIT_2:
                this.testManagerStatus();
                break;
            case KeyCode.KEY_S:
                this.switchToSceneMode();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 处理性能测试按键
     */
    private handlePerformanceKeys(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.testStartupTime();
                break;
            case KeyCode.DIGIT_2:
                this.testMemoryUsage();
                break;
            case KeyCode.KEY_S:
                this.switchToSceneMode();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 运行系统集成测试
     */
    private runSystemIntegrationTest(): void {
        console.log('🧪 ========== 系统集成测试 ==========');

        try {
            // 检查所有管理器是否已初始化
            const managers = [
                { name: 'GameManager', instance: Managers.Game },
                { name: 'SceneManager', instance: Managers.Scene },
                { name: 'EventManager', instance: Managers.Event },
                { name: 'ResourceManager', instance: Managers.Resource },
                { name: 'AudioManager', instance: Managers.Audio },
                { name: 'InputManager', instance: Managers.Input },
                { name: 'NetworkManager', instance: Managers.Network }
            ];

            let allPassed = true;
            for (const manager of managers) {
                if (manager.instance) {
                    console.log(`✅ ${manager.name} - 已初始化`);
                } else {
                    console.log(`❌ ${manager.name} - 未初始化`);
                    allPassed = false;
                }
            }

            if (allPassed) {
                console.log('✅ 所有管理器初始化测试通过');
            } else {
                console.log('❌ 部分管理器初始化测试失败');
            }
        } catch (error) {
            console.error('❌ 系统集成测试失败:', error);
        }

        console.log('🧪 ===============================');
    }

    /**
     * 测试管理器状态
     */
    private testManagerStatus(): void {
        console.log('📊 ========== 管理器状态 ==========');

        try {
            console.log('🎮 游戏管理器:', Managers.Game ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🎬 场景管理器:', Managers.Scene ? '✅ 已初始化' : '❌ 未初始化');
            console.log('📡 事件管理器:', Managers.Event ? '✅ 已初始化' : '❌ 未初始化');
            console.log('📦 资源管理器:', Managers.Resource ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🔊 音频管理器:', Managers.Audio ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🎮 输入管理器:', Managers.Input ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🌐 网络管理器:', Managers.Network ? '✅ 已初始化' : '❌ 未初始化');
        } catch (error) {
            console.error('❌ 获取管理器状态失败:', error);
        }

        console.log('📊 ===============================');
    }

    /**
     * 测试启动时间
     */
    private testStartupTime(): void {
        console.log('⏱️ ========== 启动时间测试 ==========');
        console.log('🚀 应用启动时间: 已运行');
        console.log('✅ 启动性能: 正常运行中');
        console.log('⏱️ ===============================');
    }

    /**
     * 测试内存使用
     */
    private testMemoryUsage(): void {
        console.log('💾 ========== 内存使用测试 ==========');

        try {
            if ((performance as any).memory) {
                const memory = (performance as any).memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

                console.log(`📊 已使用内存: ${usedMB}MB`);
                console.log(`📊 总分配内存: ${totalMB}MB`);
                console.log(`📊 内存限制: ${limitMB}MB`);
                console.log(`📊 内存使用率: ${Math.round((usedMB / limitMB) * 100)}%`);

                const usagePercentage = (usedMB / limitMB) * 100;
                if (usagePercentage < 50) {
                    console.log('✅ 内存使用: 正常');
                } else if (usagePercentage < 80) {
                    console.log('⚠️ 内存使用: 偏高');
                } else {
                    console.log('❌ 内存使用: 过高，需要优化');
                }
            } else {
                console.log('⚠️ 浏览器不支持内存监控API');
            }
        } catch (error) {
            console.error('❌ 内存测试失败:', error);
        }

        console.log('💾 ===============================');
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);

        // 断开WebSocket连接
        if (this._websocketClient && this._websocketClient.isConnected()) {
            this._websocketClient.disconnect();
        }

        console.log('⚔️ 战斗场景销毁');
    }
}

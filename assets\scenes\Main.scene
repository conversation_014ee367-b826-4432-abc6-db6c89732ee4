[{"__type__": "cc.SceneAsset", "_name": "Main", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "Main", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 445}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 446}, "_id": "5187d0c2-2edf-48da-91bd-597f4413becc"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 350}], "_active": true, "_components": [{"__id__": 442}, {"__id__": 443}, {"__id__": 444}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1c8+dVZW1BMLLaxjckzFW/"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f0Sbb41nlN46tr77+Qr7c2"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "34cEWObANOLLBhVDWQpqCe"}, {"__type__": "cc.Node", "_name": "MainUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}, {"__id__": 13}, {"__id__": 113}, {"__id__": 187}, {"__id__": 265}], "_active": true, "_components": [{"__id__": 348}, {"__id__": 349}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e6QLHAN31JKYXbwdgruH7C"}, {"__type__": "cc.Node", "_name": "BackPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3b3OmvX31NvqXbtO7poWpD"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1fuJzJiOFJI6H4R1A8QYH1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "32t7s5ksNOhI26iCQXcpCl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b43IvxqkxFLZ7JQNxWOzGK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2736, "_originalHeight": 4000, "_alignMode": 2, "_lockFlags": 0, "_id": "bbn4PyAAJM65U6GUf4xt0O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "20cV47r+JPy5hT/ICo2q2h"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_alignMode": 2, "_lockFlags": 0, "_id": "4fsYumWZhNV6gtLyjqCmWW"}, {"__type__": "cc.Node", "_name": "TopPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 14}, {"__id__": 46}, {"__id__": 56}, {"__id__": 65}, {"__id__": 71}, {"__id__": 81}, {"__id__": 88}, {"__id__": 95}, {"__id__": 102}], "_active": true, "_components": [{"__id__": 109}, {"__id__": 110}, {"__id__": 111}, {"__id__": 112}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 580, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e19WbD4FtCeIc1RQ+YCpiq"}, {"__type__": "cc.Node", "_name": "User_Assets", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 29}], "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}, {"__id__": 45}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 190, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "19SXI2goFDZ72O7w8yHlMd"}, {"__type__": "cc.Node", "_name": "copper_coin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 16}, {"__id__": 20}, {"__id__": 24}], "_active": true, "_components": [{"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85mu1hvgFIJbcDLYJuQ0dO"}, {"__type__": "cc.Node", "_name": "SpriteSplash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cfVj4jj4dIxrMx2c2SeEhS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8b2sC5WcJH07It3OjALkOj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 92, "g": 92, "b": 92, "a": 171}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f2Z2S5z05O77kdUtS2I6hs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_alignFlags": 21, "_target": null, "_left": 4.9339999999999975, "_right": -13.765999999999998, "_top": 30, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 150, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "a5F/KfoEhGW4yPHxzQ+Qb+"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}, {"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -40, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "45py+2qlJHiY4kiPxtuFPS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a29HkEFfJPPJUuwK+izCYN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3a31322b-b6fe-4e96-913e-d03c430ff595@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4fHJD/fXxPiKtrLIIKRAff"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_alignFlags": 10, "_target": null, "_left": 5, "_right": 40, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 20, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "64v6ya/ixFBJUr4PIiltvI"}, {"__type__": "cc.Node", "_name": "copper_coin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 26}, {"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 13.369140625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "944JPPTfRBm4rx35f/WSzA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 66.73828125, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c5oGm+XGtK6I6n25fO2oSE"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 235, "g": 209, "b": 130, "a": 255}, "_string": "100000", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "a0ZVb6TOxLTpLzXkiGO6lh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_alignFlags": 10, "_target": null, "_left": 40, "_right": 16.630859375, "_top": 0, "_bottom": 0, "_horizontalCenter": 82.3412109375, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "1fm4lpQRtBgrUlg5VMLfXn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "68znmxpdlG57I7KXjvFatl"}, {"__type__": "cc.Node", "_name": "ingots", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 30}, {"__id__": 34}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -60, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7864AgmbRIJa3ieXrCVwqH"}, {"__type__": "cc.Node", "_name": "SpriteSplash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fAM0dgndG5qpcJdMJQncT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dbb2WjpYtFpYlSfMe0/unF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 92, "g": 92, "b": 92, "a": 171}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "21eklAXcNEJ6HYXJjMtFU3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "_alignFlags": 21, "_target": null, "_left": 4.9339999999999975, "_right": -13.765999999999998, "_top": 30, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 150, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "d9siUMQeJJebPBzi9kkE+D"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -40, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "efG6q/7ChAlZd8ABFLSLwO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8aakERdnZFGY6Aen56ifxr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f5a91a4d-e1b9-46cf-86d3-b129113e6222@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6eQYvnuqdGlZhIrPSBZy00"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_alignFlags": 10, "_target": null, "_left": 5, "_right": 40, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 20, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "9470UtK19NUYoluZLP8Dxs"}, {"__type__": "cc.Node", "_name": "copper_coin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 13.369140625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7bjSzIjuNGCqcFdlZKDvAi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 66.73828125, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8ewqZAnTpBu4C48GUZrGkq"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 235, "g": 209, "b": 130, "a": 255}, "_string": "100000", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "4adIN+IAlACrTrNeSosD75"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_alignFlags": 10, "_target": null, "_left": 40, "_right": 16.630859375, "_top": 0, "_bottom": 0, "_horizontalCenter": 82.3412109375, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "bdXMMYxw9EA51hCfj+R9Fw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "60MlyPyjtEsYUVs+W4dzTs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6313UHKpJTLBkzJM+S6TA"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 30, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 1, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "eaQUym7ytLe77eZpjjYXz6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 20, "_top": 15, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "27UHOE65NI2LzfgZNumhlS"}, {"__type__": "cc.Node", "_name": "User_Power", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 47}, {"__id__": 51}], "_active": true, "_components": [{"__id__": 55}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2bOiHunjJND5yemjDtD+JF"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 48}, {"__id__": 49}, {"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -66, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0bbdw9IPpKfaOh37Wf7Cc8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b3MONKHgJBQ4aagSZE8m8A"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 210, "b": 66, "a": 255}, "_spriteFrame": {"__uuid__": "2bd87c04-ed0f-4f96-b4fc-5f1f18223b4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bbZySl/KZMs6/pujjxy3K+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_alignFlags": 8, "_target": null, "_left": -30, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "d5OwpRLf1B0pxbeUuoOL89"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4br5Lnv5VPkb1GiZfwS+xn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 97.8828125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eb8Hclb7hPXb2WHM5sctZL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "12345678", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "58H89qT+dKfqXDtU/9rJ/y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": false, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "7ciu+vMP5CZaSaiEpmAeq1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "01f741tMZC+oSf20qQGzEz"}, {"__type__": "cc.Node", "_name": "User_Lv", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 57}], "_active": true, "_components": [{"__id__": 64}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -65, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b500VVxBFOKaowbmlRZa6+"}, {"__type__": "cc.Node", "_name": "SpriteSplash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 56}, "_children": [{"__id__": 58}], "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "64Z8hpqxxLb7obupDSfEcL"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 57}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}, {"__id__": 61}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "09GPZmwftNPZhGRTfkxLcb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 35.5810546875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a3JJfHBCVODbj+Bceg8Tcr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Lv.50", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 15, "_fontSize": 15, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2bwxkWjRFInp/7FFvcHA2+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": 3, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "0ae/ZlJjBPdL1imr2NGNnI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7eoNRFC6lDh4zVbPqoQn5U"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 214, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "260amYdYlNK48s2X0QZxA/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b9SMBleuZBJY80l/+<PERSON><PERSON><PERSON>"}, {"__type__": "cc.Node", "_name": "User_Name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 66}], "_active": true, "_components": [{"__id__": 70}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -175, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9c5MIlQl9P6JC+5PZT+H9C"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 65}, "_children": [], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}, {"__id__": 69}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "80bnGj/DlLdo6vPh4Uwa7/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 129.1171875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "90sapNs3xLKZRkQoaC3LIt"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Apple Pig", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "18qwrzYmxEU7DWQm59fYOo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": -9.947265625, "_right": 0, "_top": 24.8, "_bottom": 0, "_horizontalCenter": 8, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "71Nt/lBrpOfpj5K+dDDxU5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "97kfFclhdGsJ2qsFlGKjh+"}, {"__type__": "cc.Node", "_name": "User_Icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 72}], "_active": true, "_components": [{"__id__": 80}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -289.918, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "64pDiuB9dPDLpgTagBWzI7"}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 71}, "_children": [{"__id__": 73}], "_active": true, "_components": [{"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fEUFsQxNHQLAr20uFey80"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}, {"__id__": 76}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97UlCCmPdCP7yWrp06p3an"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bbDR+WLbpLtZ1rYVZT78Aw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4f409dde-1369-4c8c-a80f-917d323bcefb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "68IpiGic9PJZxDpHm7ScJE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 312, "_alignMode": 2, "_lockFlags": 0, "_id": "ffms+9vbNCj6rxS0sMH67W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "07kT5yeE1JOqXzLe2VBTkQ"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_type": 1, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": "d7mldtEIRDXaB+v+VQSgFF"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": "d4Jd5s06xJmoNxAwqlSR6X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "faMIBmLidKDbwmIvB3ZbmT"}, {"__type__": "cc.Node", "_name": "BattleButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 82}], "_active": false, "_components": [{"__id__": 85}, {"__id__": 86}, {"__id__": 87}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": 80, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e9ZYjsJzlOaoFm8UotiBNv"}, {"__type__": "cc.Node", "_name": "BattleLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "50E/3sQS5M1ZkF3pmt+lIG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4ckzeJajhMNZxzcF6NKY1y"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "战斗", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "6f0qAKpRNDZ6cHksPKbGc1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6Ij6SnY5NM7BG2nsm1uud"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6fdrg1RHJIFoBibntUD02A"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 81}, "_id": "36ZrtF85dHn7QcHbej5TH6"}, {"__type__": "cc.Node", "_name": "MengXingButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 89}], "_active": false, "_components": [{"__id__": 92}, {"__id__": 93}, {"__id__": 94}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 150, "y": 80, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "36YzBKu4JO24P/f7k9sgav"}, {"__type__": "cc.Node", "_name": "MengXingLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "be/FPuZ5pC+7YQUh+/jyf3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d0s2UBnapJ7K9BtL1ZoOAv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "蒙性", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "70Z50EQCVPq746Yj78IVoT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fa7mgAbFhMzrHmSFIDsDqt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9e2pqisGFLlqGB/VyWZNMC"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 88}, "_id": "11iMyvfG9D9bNyrSF6t5Q8"}, {"__type__": "cc.Node", "_name": "GoblinForestButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 96}], "_active": false, "_components": [{"__id__": 99}, {"__id__": 100}, {"__id__": 101}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -159.465, "y": -80, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f0jlnYYUxPbaFVI+72lUYZ"}, {"__type__": "cc.Node", "_name": "GoblinForestLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 95}, "_children": [], "_active": true, "_components": [{"__id__": 97}, {"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ba+5nhIetDA5CA85Ifll2R"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b8pxMvaC5Hbqbu9+fJoc7E"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "哥布林森林", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "10E1VMfDpFep8RjEB/Esa2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1ekseAw6VBcINQoen+Utbx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "46fvNyn/RGWogSmYtaa/h5"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 95}, "_id": "c8wzPhB81BnrEg2f+G4W72"}, {"__type__": "cc.Node", "_name": "DarkCaveButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 103}], "_active": false, "_components": [{"__id__": 106}, {"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 150, "y": -80, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "08BWXo3mRC050zuknmrJVa"}, {"__type__": "cc.Node", "_name": "DarkCaveLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 104}, {"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8bahNv0YZFX7SgcUStqAYV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bfkGhufDtGmLYFpVZ9eYnM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "黑暗洞穴", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "8bq8lBVmdO+INeUvmmXuQY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f7dJb67g9OQILF5Il2L6+j"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b4xf8c2+1MfbUTvZUG3s5W"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 102}, "_id": "2dR170Q1xNEYAqi/37319a"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "13s/i8q+JA6qIQh3VN5iXL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 122, "g": 122, "b": 122, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9dhG1Fb8xFzqVX+nL6BXD+"}, {"__type__": "f5a08xM7nZILaHvw/boAXVt", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_panelType": "dialog", "_enableShowAnimation": true, "_enableHideAnimation": true, "_animationDuration": 0.3, "battleButton": {"__id__": 81}, "mengXingButton": {"__id__": 88}, "goblinForestButton": {"__id__": 95}, "darkCaveButton": {"__id__": 102}, "bossArenaButton": null, "trainingGroundButton": null, "_id": "5fQi7/E0JMSJDaUh9hDzQW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "6fxLVKDEhOhrNfd3EoG+f5"}, {"__type__": "cc.Node", "_name": "BehaviorPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 114}, {"__id__": 122}, {"__id__": 135}, {"__id__": 144}, {"__id__": 152}], "_active": true, "_components": [{"__id__": 183}, {"__id__": 184}, {"__id__": 185}, {"__id__": 186}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 440, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cdroq1B0hKgYiGI9og+tXV"}, {"__type__": "cc.Node", "_name": "Action_Name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [{"__id__": 115}], "_active": true, "_components": [{"__id__": 119}, {"__id__": 120}, {"__id__": 121}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -312.5, "y": 47.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cW7UJ/cxHg7VBKqOOCNLN"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 114}, "_children": [], "_active": true, "_components": [{"__id__": 116}, {"__id__": 117}, {"__id__": 118}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 116.072265625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1fitW2Fz9Pupw45u0kfpqs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 157.14453125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dfT+w2ySJApZGQPbn8Y+s5"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 104, "g": 104, "b": 104, "a": 255}, "_string": "砍树 [180次]", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "84HQQUZ35B4LaB/32oIZkF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_alignFlags": 10, "_target": null, "_left": 60, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": false, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 8, "_id": "79/8mBQXRM3pLHOjaA47Pd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7f7RlTn0hJk79CnguJHFrr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2a7dab4b-0ce2-4220-9f33-2d22f48c1be4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ceyMxtbXBOO5c+bg9KvVLX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 25, "_right": 0, "_top": 5, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "38+Z2SU8FDA6Xi2MxxyECG"}, {"__type__": "cc.Node", "_name": "ActionBarContainer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [{"__id__": 123}, {"__id__": 130}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -85, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "926wnsQcdJya0RXstRI2PZ"}, {"__type__": "cc.Node", "_name": "ProgressBarBackground", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [{"__id__": 124}], "_active": true, "_components": [{"__id__": 127}, {"__id__": 128}, {"__id__": 129}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eeWOZgt8hCzowEKsJkKHsE"}, {"__type__": "cc.Node", "_name": "ProgressBarForeground", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 126}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -250, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a9ILPZ0dpA0ru/8CTMsmWx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "c2RTHEvqFAmKPmW2B8wYbA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 177, "g": 94, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "24a704da-2867-446d-8d1a-5e920c75e09d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d69kHBwChOR4XgK2N5VhUw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "09iAwiuL1KDoKdRUGOrejs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9fd900dd-221b-4f89-8f2c-fba34243c835@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "67D1VK/1BAH7HOkGAlq3Ap"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_barSprite": {"__id__": 126}, "_mode": 0, "_totalLength": 500, "_progress": 0, "_reverse": false, "_id": "316obL2vBEXZYUGF/Zj5XE"}, {"__type__": "cc.Node", "_name": "ProgressLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [], "_active": true, "_components": [{"__id__": 131}, {"__id__": 132}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "acGx5rRj1Ot4GAh2ijikDK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 130}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 99.6708984375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8d3iiHrP1JL6xpVwwzYzTr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 130}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "准备中...", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0d7hDHMhtNrKuABsBStVys"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0clrZba0tARqOLWTXee7at"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 25, "_right": 0, "_top": 25, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 9, "_id": "3a9QhjYRxFi6tsCefQagB6"}, {"__type__": "cc.Node", "_name": "StopButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [{"__id__": 136}], "_active": true, "_components": [{"__id__": 139}, {"__id__": 140}, {"__id__": 141}, {"__id__": 143}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 260, "y": 30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b0UP1BLs5MW5D4A4pYQn4m"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 135}, "_children": [], "_active": true, "_components": [{"__id__": 137}, {"__id__": 138}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1aqUEJaQBLVoxFbnz9GDdw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 136}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ccRBApXGRLxZ4Gi+6XoALJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 136}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "停止", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d42Mxa3BtEFJF3nkXTPdpR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "41WWh47b9AkpH3kJRD0vhp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 92, "b": 31, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3f2ZOLJgJHbrrs6hYwM7Ew"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 142}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 135}, "_id": "d4sWHHtGhOlJ6fQyOdInNx"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 113}, "component": "", "_componentId": "d801bd5veNFwbDXnBSz1uUy", "handler": "stopAndResetProgress", "customEventData": "stop_reset"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 135}, "targetLabel": {"__id__": 136}, "scaleTargets": [], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 223, "g": 92, "b": 31, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 223, "g": 92, "b": 31, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 223, "g": 92, "b": 31, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 220, "g": 220, "b": 220, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1, "duration": 0.1, "_id": "65HgCRMY9E1LsYjJapls2S"}, {"__type__": "cc.Node", "_name": "QueueB<PERSON>on", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [{"__id__": 145}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 149}, {"__id__": 150}, {"__id__": 151}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 260, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46578jvmhPjbbGKz6/C87n"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [], "_active": true, "_components": [{"__id__": 146}, {"__id__": 147}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85pUkLjy5FgIc5+R2UDxIA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "38QoYjtP1B9LxLjmzNLaNH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "队列", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "c1b6FneaVPoZwUtNFVqf4E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "82faxyF29G64OEvByDBfVt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 100, "g": 200, "b": 200, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8eqr5e8mxF5YqFsgLTf4mY"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 144}, "_id": "fdztdSUdFLT6ETUp58pGUy"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 144}, "targetLabel": {"__id__": 145}, "scaleTargets": [], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 100, "g": 200, "b": 200, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 100, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 100, "g": 200, "b": 200, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 220, "g": 220, "b": 220, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1, "duration": 0.1, "_id": "06wehvN45CzZyOFHBnun04"}, {"__type__": "cc.Node", "_name": "BehaviorButtons", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [{"__id__": 153}, {"__id__": 160}, {"__id__": 167}, {"__id__": 174}], "_active": false, "_components": [{"__id__": 181}, {"__id__": 182}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -234.325, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c6yO5UfOlLzKfrVcbGWBc4"}, {"__type__": "cc.Node", "_name": "AttackButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [{"__id__": 154}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 158}, {"__id__": 159}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -10, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d5wvDbXX9FV4w22oKFltSK"}, {"__type__": "cc.Node", "_name": "AttackLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 153}, "_children": [], "_active": true, "_components": [{"__id__": 155}, {"__id__": 156}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b10GWpyb5By6V67vBPGAmR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dfPtEVWyZGw7y1oyM7Y+sz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "攻击", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "18mJbgie1M0rrei7P+c1sx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fcGpgYLmtNuZ83TFpmYGyP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 200, "g": 100, "b": 100, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "64rLmgNu5CHp2lLZ4XMzHo"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "27juBReBFOjIiufXcbp46X"}, {"__type__": "cc.Node", "_name": "DefendButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [{"__id__": 161}], "_active": true, "_components": [{"__id__": 164}, {"__id__": 165}, {"__id__": 166}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 80, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a0wJtJfsxHoLOKBiyrZBoH"}, {"__type__": "cc.Node", "_name": "DefendLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 160}, "_children": [], "_active": true, "_components": [{"__id__": 162}, {"__id__": 163}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9dkloCA3pPerr5J/XLuGuI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4cSFdqLV9JL6W4m5s38kOf"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "防御", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "d7v2ImZkNAV4/BO4Amc1sa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "17bBTZRsFOfojh2CM8eFcp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 100, "g": 100, "b": 200, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "abf5GzJqpJbIlHFyHfnium"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "26QyHFcA5Kz4iYy8VFBUUK"}, {"__type__": "cc.Node", "_name": "SkillButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [{"__id__": 168}], "_active": true, "_components": [{"__id__": 171}, {"__id__": 172}, {"__id__": 173}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7aP+GmAKNAOq9YhyZoCfkz"}, {"__type__": "cc.Node", "_name": "SkillLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [{"__id__": 169}, {"__id__": 170}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03ZJ0322dEDrBNOUSJ9plM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "64BuweEW1C6Kel1Q4pheIX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "技能", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2boIL6zuRJKJD7HVAJvtcw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "75W1ebfbRAxIodok8A/gji"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 150, "g": 100, "b": 200, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2921zVhvJCMpC2lJQZSty3"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "5fb3QNHxJIIaASrVq19cVV"}, {"__type__": "cc.Node", "_name": "EscapeButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [{"__id__": 175}], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}, {"__id__": 180}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 260, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "645htTiItF6baLusGfBMXL"}, {"__type__": "cc.Node", "_name": "SkillLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 174}, "_children": [], "_active": true, "_components": [{"__id__": 176}, {"__id__": 177}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1BxDWVnFOj4B1PQgQPic3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "19kFGfqRNGFLFJ4MO0cpX9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "逃跑", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "34rbG88MxJMInRAYMRFht9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7ej+sjHQdMoqPWKVwXfs4C"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 100, "b": 100, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a8jX8F0ftExaPJT0Tnci7S"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "65vrfIfKNDu6Zbet2LByxQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5bqzrkSKZB5KBdiEi424Io"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "47YyEG/hpKB5QLJbRVy7Lq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "daYbD/HV1OBbVlTXPgELsz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 189, "g": 189, "b": 189, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c7M+cyc01CnJgxcqHsbAx1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 125, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 680, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 41, "_id": "62wj72kzpI9KtonXMxW5k6"}, {"__type__": "d801bd5veNFwbDXnBSz1uUy", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "progressBar": {"__id__": 129}, "progressLabel": {"__id__": 132}, "animationContainer": {"__id__": 122}, "defaultDuration": 5, "autoStart": true, "loop": true, "updateFPS": 60, "enableSmoothing": true, "smoothingFactor": 0.9, "_id": "8fOg5SZEJLIb2TPVM2Yrup"}, {"__type__": "cc.Node", "_name": "MapPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 188}, {"__id__": 198}], "_active": true, "_components": [{"__id__": 261}, {"__id__": 262}, {"__id__": 263}, {"__id__": 264}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -460, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2cNpMPit9MabByNJvkCpBy"}, {"__type__": "cc.Node", "_name": "Switch", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [{"__id__": 189}], "_active": true, "_components": [{"__id__": 193}, {"__id__": 194}, {"__id__": 195}, {"__id__": 196}, {"__id__": 197}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -310, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7dCD0Xdq1MZqrYzi/peXZ/"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 188}, "_children": [], "_active": true, "_components": [{"__id__": 190}, {"__id__": 191}, {"__id__": 192}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "34ICPZUeBC7puKtc0Kncag"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dc2nJXp99IPbFKSUkOzU8r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b0a7f1c1-d435-49fc-a71b-d5d1960d7530@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f1wcGQ7+tBb7fYg4rEhx5f"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_alignFlags": 18, "_target": null, "_left": -231, "_right": -231, "_top": 20, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "0e9lWNWx5Fuq1z1pwe2Um6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "041QGqZYVHAaGEwEWM9q6k"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 188}, "_id": "4fgsfGyd9E9rDO/nLABcv7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "_alignFlags": 13, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "65LVj0PApP0YZHzmom7mXn"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 189}, "targetLabel": null, "scaleTargets": [{"__id__": 189}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1, "duration": 0.1, "_id": "9462zqqFxNh4x5ysS9l4Ob"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 141, "g": 141, "b": 141, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "14YHxsliNFGL/2jBF/x34r"}, {"__type__": "cc.Node", "_name": "Maplist", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [{"__id__": 199}], "_active": true, "_components": [{"__id__": 259}, {"__id__": 260}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 50, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "89ihXVK75JmrURr4LZHeqG"}, {"__type__": "cc.Node", "_name": "list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [{"__id__": 200}, {"__id__": 212}, {"__id__": 224}, {"__id__": 236}], "_active": true, "_components": [{"__id__": 257}, {"__id__": 258}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1exKY6pt9CuaqmRR1EOeSf"}, {"__type__": "cc.Node", "_name": "map", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 201}, {"__id__": 205}], "_active": true, "_components": [{"__id__": 209}, {"__id__": 210}, {"__id__": 211}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -350, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "faVK1yiiFLoK4hh0kGPOz2"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 203}, {"__id__": 204}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beCqML5GZMX6+iUdHcc/+s"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 85, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "714uPgB9VPN4pGzvdQk5fR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f8e01919-a246-4dae-921c-401aec66ca1f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a543TyoGZOebacQ03vcEtr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 52.5, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "78tLdZihZIapiWwWz6vfmH"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 206}, {"__id__": 207}, {"__id__": 208}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d5YGR1ok1Ei4MS2JAdMe2q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bcHmeSDVdKkYgChLWRchs1"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 73, "g": 73, "b": 73, "a": 255}, "_string": "地点名称", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "572YJrZcVLeoMvke5th6qk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 105, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "f8Gff+gRNG+a99ysYHK2wQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3dATdtxapFeZQB2WGHjkIo"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 200}, "_id": "ffzql5n5hK7JtBHcvIf/mA"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 201}, "targetLabel": {"__id__": 205}, "scaleTargets": [{"__id__": 201}, {"__id__": 205}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "00HmIEcwhBBK3P1elqzHZQ"}, {"__type__": "cc.Node", "_name": "map-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 213}, {"__id__": 217}], "_active": true, "_components": [{"__id__": 221}, {"__id__": 222}, {"__id__": 223}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -200, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13SMSjcidDOrkg7Vr3zQoL"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 214}, {"__id__": 215}, {"__id__": 216}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b8jsY/5LhPYI8qq3/urHXD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 85, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1eOy0giwFALYT0LGcPRvKJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f8e01919-a246-4dae-921c-401aec66ca1f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cbUBTP/cVIGoahrrgLelAe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 52.5, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "49hlerGVBAcIXv2A2DqsTV"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 218}, {"__id__": 219}, {"__id__": 220}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6biezINj1GmZfO41/LnMSd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 217}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6fMgFyXEhJsLlMvDRYccCq"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 217}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 73, "g": 73, "b": 73, "a": 255}, "_string": "地点名称", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "6aRCfQHThP8pxUY7eogYJV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 217}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 105, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "3dOfWe0opPbpFMnTm71VHc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c6YLST5fZJaJpyyb3l9GAF"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 212}, "_id": "cbEB714fZMdqrBRI3tlDSN"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 213}, "targetLabel": {"__id__": 217}, "scaleTargets": [{"__id__": 213}, {"__id__": 217}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "e8axykfi5HWo9wg54sG7hl"}, {"__type__": "cc.Node", "_name": "map-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 225}, {"__id__": 229}], "_active": true, "_components": [{"__id__": 233}, {"__id__": 234}, {"__id__": 235}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -50, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "30ZJsJGmZAT6oSZVGauJng"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 224}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}, {"__id__": 228}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c8qQQnh2RCKob3wsYY2OA2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 85, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f4hNciPrRCuIjNqwa0KF3O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f8e01919-a246-4dae-921c-401aec66ca1f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "83NNJCeytLY5nHAYEcypj0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 52.5, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "aaqZGNMLxPLbh7m8zvBcYO"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 224}, "_children": [], "_active": true, "_components": [{"__id__": 230}, {"__id__": 231}, {"__id__": 232}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "00dFmnw0VMHIA6GIyTyaL4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0ee5JRZBRHLqX+B7iwI+GH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 73, "g": 73, "b": 73, "a": 255}, "_string": "地点名称", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "230Bl2i9NHsLgAtibAAiL0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 105, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "d7BxlHdzJAp751zNRlb88r"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0b+QrJIt5FWK+SF7YGvFGm"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 224}, "_id": "9bu2Wd1L9AKL+I+pM8jwBn"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 225}, "targetLabel": {"__id__": 229}, "scaleTargets": [{"__id__": 225}, {"__id__": 229}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "78ZWl6tCdCb74gXXzyFdT0"}, {"__type__": "cc.Node", "_name": "HorizontalScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 199}, "_children": [{"__id__": 237}], "_active": true, "_components": [{"__id__": 255}, {"__id__": 256}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5a+9qxs8pPrqnY0pDw48Xi"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 236}, "_children": [{"__id__": 238}], "_active": true, "_components": [{"__id__": 252}, {"__id__": 253}, {"__id__": 254}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "76Dx2s/E1DjaYe2mtNyUQ7"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 237}, "_children": [{"__id__": 239}, {"__id__": 242}, {"__id__": 245}, {"__id__": 248}], "_active": true, "_components": [{"__id__": 251}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d02Uu+uf1G0q9erEQJQ5Mp"}, {"__type__": "cc.Node", "_name": "MapItem1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 238}, "_children": [], "_active": true, "_components": [{"__id__": 240}, {"__id__": 241}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 50, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ffeWeygkhIuZr2d313Rcyh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9dYG2DH/9DgYo75RBCV9EF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7aTTgFMvdHtJb51jK1REAR"}, {"__type__": "cc.Node", "_name": "MapItem2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 238}, "_children": [], "_active": true, "_components": [{"__id__": 243}, {"__id__": 244}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f6odPp2s5IEKscI0U0PqAL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f5rX0h3LVMzrCua/4SniIw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "98aaGN9KRFQKDaPZQpzEah"}, {"__type__": "cc.Node", "_name": "MapItem3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 238}, "_children": [], "_active": true, "_components": [{"__id__": 246}, {"__id__": 247}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 250, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1eu4BJG61K27nhb58sOrSl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dbYj5YhY1PurwKEcG/J0wh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f2w/K6Y25BaKzOeZs8kBBt"}, {"__type__": "cc.Node", "_name": "MapItem4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 238}, "_children": [], "_active": true, "_components": [{"__id__": 249}, {"__id__": 250}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 350, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a0DXAtJctLnaNBH7crYKqY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fbeGTQfSBP+6DD3x0jD4Qy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "41Jnb11jFAV7xKn0D5fi7M"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 238}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": "16K4cn98NHHq0yKnjxg8+v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a0q9qLt8hD8rLjLjzfnTNi"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": null, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": "24JVUZWDZKJon0LgTc3TDM"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": "e6y66syv1OGrzGUWeSe1bJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 236}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "79O13uwCpMJJTWpw3AAV8/"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 236}, "_enabled": true, "__prefab": null, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": true, "vertical": false, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 238}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": "d4mWnFOB5HUZeoOw74Hovb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 850, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "53zkEkqTxLn6PV0syxMQQr"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "2byfruNq5K6bPXijGOz/lF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 620, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fa5vcYNq9N06k+UyDVqIz1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_alignFlags": 13, "_target": null, "_left": 100, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 13, "_id": "aeFKmZFF9B8YEh63634ta9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a1957q2zZLh5P+VlMRK+td"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 189, "g": 189, "b": 189, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4ec6oqCsZFDZzrHhF38jxf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 105, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 120, "_alignMode": 2, "_lockFlags": 0, "_id": "83cdVvm/tNpIb3okEO02tC"}, {"__type__": "db9cfr5pztMyocSxPjUkhMD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": null, "_id": "b6iMzBvSVJMqVCi6LAvPu5"}, {"__type__": "cc.Node", "_name": "BottomPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [{"__id__": 266}], "_active": true, "_components": [{"__id__": 344}, {"__id__": 345}, {"__id__": 346}, {"__id__": 347}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -590, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4014RxX+tPRrjQcr0E0J5v"}, {"__type__": "cc.Node", "_name": "BottonIcon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 265}, "_children": [{"__id__": 267}, {"__id__": 282}, {"__id__": 297}, {"__id__": 312}, {"__id__": 327}], "_active": true, "_components": [{"__id__": 342}, {"__id__": 343}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -17.212, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "430s60jkVMzaFMPsM6h1Qq"}, {"__type__": "cc.Node", "_name": "User", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 268}], "_active": true, "_components": [{"__id__": 281}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -300, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3czu+TfZFNuo0JfnHU8/0h"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [{"__id__": 269}, {"__id__": 273}], "_active": true, "_components": [{"__id__": 277}, {"__id__": 278}, {"__id__": 279}, {"__id__": 280}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "93bMFVGqVO57SXYKIt66nA"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 268}, "_children": [], "_active": true, "_components": [{"__id__": 270}, {"__id__": 271}, {"__id__": 272}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46PvsOOLZHG6NV6SnFLHPS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6as256qgRKTK3ANNrLmziR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a79bNWVt1IOIjT/IcZ4lRn"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 3, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "8b1ILAKc9MhrYp78PkQlc4"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 268}, "_children": [], "_active": false, "_components": [{"__id__": 274}, {"__id__": 275}, {"__id__": 276}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "96kJ9VpnFLpJsWRPIq7bzh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "90daik6PBMoLC6jKtI7wUC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "角色", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "02aEnXPb5KTIZJbcB4Tzpi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -10, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "f2+vGNb6xCw6APq3e7S/ry"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "33mT3u2QNNS7+hPEHaMimu"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 268}, "_id": "dbBBHh+4NNha3ZBE1fAMyg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 2, "_lockFlags": 0, "_id": "133t0NAj1Ma4bGqstxyrv+"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 269}, "targetLabel": {"__id__": 273}, "scaleTargets": [{"__id__": 269}, {"__id__": 273}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "79AuUbC2RMQJeYPKCvwyq+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bdXBgQYTlISIjEj8P3G9Up"}, {"__type__": "cc.Node", "_name": "Bag", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 283}], "_active": true, "_components": [{"__id__": 296}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "99NSGyQNBPtbZLoyosxSFh"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 282}, "_children": [{"__id__": 284}, {"__id__": 288}], "_active": true, "_components": [{"__id__": 292}, {"__id__": 293}, {"__id__": 294}, {"__id__": 295}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "32/LUMLwNINofoQQoLX6Xp"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 283}, "_children": [], "_active": true, "_components": [{"__id__": 285}, {"__id__": 286}, {"__id__": 287}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "84psJyxxFDKYVxCPCrNupN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "64cEmC/bhAXqwmDXl+F9OH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a86055ba-9b20-4c63-bf3c-d828108ec742@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4fWxV3/zFEUr9mQ6RfCPST"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 3.9999999999999964, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "74puvHJItGTa0IzN5VxTY0"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 283}, "_children": [], "_active": false, "_components": [{"__id__": 289}, {"__id__": 290}, {"__id__": 291}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b0r5L/w6BMw521/OeuVKsi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e0BZdUD7ND0L5of7CI6ZD7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "背包", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "c2A/JFRcBMF43ew6Wq/zqz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -10, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "d8NHD/EmFGWqFSyTkNBdRQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "54T0zjEl9FG5AW2WfTjN71"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 283}, "_id": "9c5/KTRDFMVYw24/z+r06v"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 2, "_lockFlags": 0, "_id": "d5EN7AxLtCqrAOg2dcRLvQ"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 284}, "targetLabel": {"__id__": 288}, "scaleTargets": [{"__id__": 284}, {"__id__": 288}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "98YAHnUXNLpIs2eW51ENj+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77vtIxkpBGnKTDhyaAYYPd"}, {"__type__": "cc.Node", "_name": "Map", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 298}], "_active": true, "_components": [{"__id__": 311}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "70YrGj4GZL3IboB6Wx44O+"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 297}, "_children": [{"__id__": 299}, {"__id__": 303}], "_active": true, "_components": [{"__id__": 307}, {"__id__": 308}, {"__id__": 309}, {"__id__": 310}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b2PuQuqIFKhJle+ap6Z6IQ"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 300}, {"__id__": 301}, {"__id__": 302}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a9cPehqmJCXalQ24V5zndX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bLGUA/E9AfqXXiRTesdTo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "aa53920a-995f-46da-9a5e-62a3f1c1c994@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "0aT7Ir3/hKGJMxOZ9cMwbZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 3.9999999999999964, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "1cNUPsAjNLDbstULP9vz7C"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 298}, "_children": [], "_active": false, "_components": [{"__id__": 304}, {"__id__": 305}, {"__id__": 306}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "59aMlK8nVDLoGI6jXE3lMH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fb3MUM7thKf4RzsOZAiY4E"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "世界", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "09J3I/YttECZKa6uvkzVR0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -10, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "fdIn1AXkRC7bh+JV/jcjOA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ebS9LKF1dNR76A4xTvFE2p"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 298}, "_id": "41ZZOHV/hEZ6jYhTC3WFat"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 2, "_lockFlags": 0, "_id": "5czD8bnV1BIIYZKqFN1z2h"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 299}, "targetLabel": {"__id__": 303}, "scaleTargets": [{"__id__": 299}, {"__id__": 303}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "4aa+Cy6q1GCYBgfBPQiwbT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2fsENwHTlPEYU/Y1xN2Rxz"}, {"__type__": "cc.Node", "_name": "Shop", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 313}], "_active": true, "_components": [{"__id__": 326}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 150, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56fW5SknFMwrrm54bV50IA"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 312}, "_children": [{"__id__": 314}, {"__id__": 318}], "_active": true, "_components": [{"__id__": 322}, {"__id__": 323}, {"__id__": 324}, {"__id__": 325}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8dR95Dq6lC6Y6N5RnCHSF0"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 313}, "_children": [], "_active": true, "_components": [{"__id__": 315}, {"__id__": 316}, {"__id__": 317}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dbFz8lt5dN+YYu1u66ncmw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 57}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b9JubjuXxBLr+WNkRUnMPz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ccd322f-1c53-4b1f-a6e7-f98fdae12a31@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c0p4mghy9GYJhXcD4aoxpM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 314}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 6.5, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "1aWYrgwddPopGJOYZZKzra"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 313}, "_children": [], "_active": false, "_components": [{"__id__": 319}, {"__id__": 320}, {"__id__": 321}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b4FqCFQnhPNrMOojZyRkiQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ce4c8DVU9CJ4XLUHQkCQcH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "商店", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "164YeGqBxJg7GUu+lsgO31"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -10, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "dd9hxqKsFAJIpeuw8Wtb+9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "19KWrr3m5OmYjqRlzFtsIr"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 313}, "_id": "693l7gkTtCnLlqw05KxHGF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 2, "_lockFlags": 0, "_id": "5dWlEzNepM9rqyndpwjeTY"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 314}, "targetLabel": {"__id__": 318}, "scaleTargets": [{"__id__": 314}, {"__id__": 318}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "83+oo55nhInKdRcYhPOtdR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2b/g++Qm1B8aEM05UAMm9d"}, {"__type__": "cc.Node", "_name": "Else", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 328}], "_active": true, "_components": [{"__id__": 341}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 300, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1a/18W1EVD2aI+FUeKg6Ev"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 327}, "_children": [{"__id__": 329}, {"__id__": 333}], "_active": true, "_components": [{"__id__": 337}, {"__id__": 338}, {"__id__": 339}, {"__id__": 340}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13qFLBgB9HDbtidY0HPArc"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 328}, "_children": [], "_active": true, "_components": [{"__id__": 330}, {"__id__": 331}, {"__id__": 332}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4d/NKVL/ZGeIaHde0V7g1S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "faA4uMkStOcoHmVlxPBUpW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c122c4fb-a5bc-4798-a7f1-42d4229969f0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "00nW/86TFI9KF8SYLBY1Ba"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": null, "_alignFlags": 17, "_target": null, "_left": -231, "_right": -231, "_top": 9, "_bottom": 18, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 323, "_originalHeight": 64, "_alignMode": 2, "_lockFlags": 0, "_id": "9fTYE7wQxNQ6KBQ7geGrlB"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 328}, "_children": [], "_active": false, "_components": [{"__id__": 334}, {"__id__": 335}, {"__id__": 336}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d4BC+CKyZOJ6rD82C/v8fS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 333}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f23u/cIWdO87zANH9pt2R8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 333}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "其他", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "32J2A4c5RIX7bpoEQNHGBA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 333}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -10, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "e4eVjoEB1E2biEm/W2uw+8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "83kUMrQ79P5r3yqSoKF/jh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "125764b6-8999-449e-89a1-784bdcca9f53@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 328}, "_id": "8doqTyMzBOiYKAiRRLZ8dg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 2, "_lockFlags": 0, "_id": "c4m1Vtv7xFRp+epOon79vv"}, {"__type__": "9c90fSAPhpJJ6gRqvNMy+1Y", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": null, "targetSprite": {"__id__": 329}, "targetLabel": {"__id__": 333}, "scaleTargets": [{"__id__": 329}, {"__id__": 333}], "enableSpriteColorEffect": true, "enableLabelColorEffect": true, "enableScaleEffect": true, "spriteNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "spritePressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "spriteHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelNormalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "labelPressedColor": {"__type__": "cc.Color", "r": 180, "g": 180, "b": 180, "a": 255}, "labelHoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "normalScale": 1, "pressedScale": 0.95, "hoverScale": 1.05, "duration": 0.1, "_id": "d0asdRNbFFy5omXl1N01G1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4du9X9KkBBlq8S1XWmx8zL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c0lYJv2pRBUaam3Q6+rHzm"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 50, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "4cWVGnij9Oi5vhVzdAX6ni"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "eeu4TBs/FDmKWH95pcTPhi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 126, "g": 126, "b": 126, "a": 255}, "_spriteFrame": {"__uuid__": "aa10b9f8-7682-45af-b01e-dc661f69d1b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6eM8e7a9dDdYLKpoCFSPd8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 120, "_alignMode": 2, "_lockFlags": 0, "_id": "dfxQl0+1FP2qIfqL+/m0Y6"}, {"__type__": "db9cfr5pztMyocSxPjUkhMD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": null, "_id": "3e2mvwFJRFu5w6CKp3FNHe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8eH5OHHfBOeLKt6oX1Z9pW"}, {"__type__": "d6015votZRPBobM8OF+a7zO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": false, "__prefab": null, "leftPanel": {"__id__": 13}, "rightPanel": {"__id__": 113}, "centerContent": {"__id__": 6}, "_id": "a5GVze0rJG2Lzgd/7x6n7d"}, {"__type__": "cc.Node", "_name": "EquipmentPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 351}, {"__id__": 395}, {"__id__": 427}], "_active": false, "_components": [{"__id__": 439}, {"__id__": 440}, {"__id__": 441}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "09364RgVJLWb3tlbByn6eM"}, {"__type__": "cc.Node", "_name": "EquipmentSlotsContainer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [{"__id__": 352}, {"__id__": 359}, {"__id__": 366}, {"__id__": 373}, {"__id__": 380}, {"__id__": 387}], "_active": true, "_components": [{"__id__": 394}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -200, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fd0JockAZDrJpIaCBSKbxa"}, {"__type__": "cc.Node", "_name": "HelmetSlot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [{"__id__": 353}], "_active": true, "_components": [{"__id__": 356}, {"__id__": 357}, {"__id__": 358}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8ed0ZG9TVAL5gSDSkXST2i"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 352}, "_children": [], "_active": true, "_components": [{"__id__": 354}, {"__id__": 355}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6cN1OfajlGbbxBzLYU9OJe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aci3asQaJNUIT7H3h4IB07"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "头盔", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 14, "_fontSize": 14, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "bbNkyeb2pDN4/egMlWGXSb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "09X+kc4GdI1bWuZNgbXWTG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 80, "g": 80, "b": 80, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cajkPUJJ9Pg5vbWylLv5u0"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "d4asDjHPFDyJ7U++6MKZW/"}, {"__type__": "cc.Node", "_name": "WeaponSlot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [{"__id__": 360}], "_active": true, "_components": [{"__id__": 363}, {"__id__": 364}, {"__id__": 365}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -100, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d0YNiiWzlCA6Tfo4AYo+1x"}, {"__type__": "cc.Node", "_name": "WeaponLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 359}, "_children": [], "_active": true, "_components": [{"__id__": 361}, {"__id__": 362}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "25DMS4vP9FwJjWn/u6RBEa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 360}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "96wz7cvbNGkKi3DkkOEpoM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 360}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "武器", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 14, "_fontSize": 14, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2c6qr6C41OjIWkoLawtDdK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e6pYdbVAxCF7vf10fAVoP5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 80, "g": 80, "b": 80, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2f3QZ64uhMS7UBC6sujyu/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "84enQ63mNLiqxnymuN278T"}, {"__type__": "cc.Node", "_name": "ArmorSlot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [{"__id__": 367}], "_active": true, "_components": [{"__id__": 370}, {"__id__": 371}, {"__id__": 372}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3b6XcYUCZPKKB1tm3Gb+QD"}, {"__type__": "cc.Node", "_name": "ArmorLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 366}, "_children": [], "_active": true, "_components": [{"__id__": 368}, {"__id__": 369}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "72xi21L8tKu7ZfzaSYydbb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1fTEoi6D5Cp6uS6Ct24FB7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "护甲", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 14, "_fontSize": 14, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "afS1VfMfNH4bK6RAiujpDt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "38cB/cyCBD472JVf0uxdn/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 80, "g": 80, "b": 80, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ba/4Eq+t1H1JRsl635ohjs"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "9d8qfSiJlPsIciMq/FwwXS"}, {"__type__": "cc.Node", "_name": "BootsSlot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [{"__id__": 374}], "_active": true, "_components": [{"__id__": 377}, {"__id__": 378}, {"__id__": 379}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "61/qNm/NhA9JaUQVTeb/np"}, {"__type__": "cc.Node", "_name": "BootsLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 373}, "_children": [], "_active": true, "_components": [{"__id__": 375}, {"__id__": 376}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c2u7QusPtHioJY6Qfsd4HE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c6WwHsfthOgYhAfL/ODxiA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "鞋子", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 14, "_fontSize": 14, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "35EPQirCtNa43mpuKQ/XtR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43qu/C2QVGmIAISIkzLWmm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 80, "g": 80, "b": 80, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "48r/VJkrtODomE5P7b0VC9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "707nppHQtKV48Fzs3r3Z1o"}, {"__type__": "cc.Node", "_name": "RingSlot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [{"__id__": 381}], "_active": true, "_components": [{"__id__": 384}, {"__id__": 385}, {"__id__": 386}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 100, "y": 75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ad+lEBBSpAFYHjuiqVzXPT"}, {"__type__": "cc.Node", "_name": "RingLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [], "_active": true, "_components": [{"__id__": 382}, {"__id__": 383}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37feEfjelKTZs8Gpl6Uio8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fc3A87PvJDCYvXYTbO/Vhq"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "戒指", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 14, "_fontSize": 14, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0bXrFEEcVEKIyYv4H28LYF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b3i/rnpIRKpa8Sq30zbpD8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 80, "g": 80, "b": 80, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "efJXb1ndZO5qVLm615P0+T"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "e2GPe7lONNHoiO+APL0gze"}, {"__type__": "cc.Node", "_name": "AmuletSlot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 351}, "_children": [{"__id__": 388}], "_active": true, "_components": [{"__id__": 391}, {"__id__": 392}, {"__id__": 393}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 100, "y": -75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "98vzvPLCZAQ4Hb83/D9elH"}, {"__type__": "cc.Node", "_name": "AmuletLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 387}, "_children": [], "_active": true, "_components": [{"__id__": 389}, {"__id__": 390}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fbNLudD39O77nE4z+0Qvl4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "85Wjbd259PF6IvKoXiDc7S"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "护身符", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 14, "_fontSize": 14, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "17lSm/fTxH7aQaKH9wOY7T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 387}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "22CyaiuEFFHrOlUoQg9pW8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 387}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 80, "g": 80, "b": 80, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dbH4dXfRNLHaiIQZ45n2s+"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 387}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "40g6wfdT1MGbAVeUswmPWE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 351}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77iTAimghEMYvHjF1S7rPz"}, {"__type__": "cc.Node", "_name": "EquipmentInventoryContainer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [{"__id__": 396}], "_active": true, "_components": [{"__id__": 424}, {"__id__": 425}, {"__id__": 426}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 250, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "846pk76J1Pq5avQLJDspE2"}, {"__type__": "cc.Node", "_name": "InventoryScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 395}, "_children": [{"__id__": 397}], "_active": true, "_components": [{"__id__": 421}, {"__id__": 422}, {"__id__": 423}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7eo5MqjD1BprIaQpKp9Acb"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 396}, "_children": [{"__id__": 398}, {"__id__": 405}, {"__id__": 412}], "_active": true, "_components": [{"__id__": 419}, {"__id__": 420}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6cyFrAn55ITLrYwGqcsCHY"}, {"__type__": "cc.Node", "_name": "EquipmentItem1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [{"__id__": 399}], "_active": true, "_components": [{"__id__": 402}, {"__id__": 403}, {"__id__": 404}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "927Wl3dnNDQrZJCcNjZrcK"}, {"__type__": "cc.Node", "_name": "ItemLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 398}, "_children": [], "_active": true, "_components": [{"__id__": 400}, {"__id__": 401}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b74ijoOSxOwqi+7ZqLvD84"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 399}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6eUXrkQvFKkowRKleXbGUc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 399}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "铁剑", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "b8YpOjqUVA47Fb6Rc/LWT1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "18NrCQZD9IMI4oxFo305BE"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "06Jxr5ZvVPmrdy32xPZVXR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ccV1ggfkBHXbSDmAhK8uUX"}, {"__type__": "cc.Node", "_name": "EquipmentItem2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [{"__id__": 406}], "_active": true, "_components": [{"__id__": 409}, {"__id__": 410}, {"__id__": 411}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -115, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "209SJMN+xKJYdwY8JWnaEf"}, {"__type__": "cc.Node", "_name": "ItemLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 405}, "_children": [], "_active": true, "_components": [{"__id__": 407}, {"__id__": 408}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "525WmQiSlH2aTYQFy/4o/l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 406}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c3KLSYhhNN4IFq5nOVheAP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 406}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "皮甲", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "94fT6Ib2lDKpTaB/bATSrV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 405}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "765LX819dHv6jZX2nxtaXa"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 405}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "01cMrFrnlLtrWacKtugHUc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 405}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "77Vv8Gt39Kt6E1Tc69ThFS"}, {"__type__": "cc.Node", "_name": "EquipmentItem3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 397}, "_children": [{"__id__": 413}], "_active": true, "_components": [{"__id__": 416}, {"__id__": 417}, {"__id__": 418}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -220, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65zXop/CZDI6mFi3SN1RpQ"}, {"__type__": "cc.Node", "_name": "ItemLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 412}, "_children": [], "_active": true, "_components": [{"__id__": 414}, {"__id__": 415}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3aClW28aZCSJJ6V9YNDeuN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 413}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "93Xva/Ar9I0Kdb8K+cnyxr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 413}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "魔法帽", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "c9mlwdhadEcL65gcGtjIKd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 412}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d5oJFzJzlK7KxmdD/lg6lj"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 412}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "a0m+RNckJLWIB26eT9kIbP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 412}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dcChZQf/RMbIOMsZiXAAUc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "59lgJoX+NGkYmYF769Y+fZ"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 397}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 10, "_paddingBottom": 10, "_spacingX": 5, "_spacingY": 5, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "72Xy9yQ1ZP1pa3BWKRzAtf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8fbFAKNzxKZb37s0ufgqmA"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": null, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": true, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": null, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": "88v/xhlGJH9Lulo+mcHKD6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "09Oz1XuF5PhZ3i5bd0X8Q8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 395}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a0E1d9GrdC2bg669qykg9Z"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 395}, "_enabled": true, "__prefab": null, "_resizeMode": 0, "_layoutType": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "402OuZF0FISqt9uS4qoxDU"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 395}, "_enabled": true, "__prefab": null, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": true, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": null, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": "c3vKtvjxhCWrxU0vt9XQ4z"}, {"__type__": "cc.Node", "_name": "EquipmentStatsPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 350}, "_children": [{"__id__": 428}, {"__id__": 431}, {"__id__": 434}], "_active": true, "_components": [{"__id__": 437}, {"__id__": 438}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -250, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "575n1mXOZO94c2YoVKpF1I"}, {"__type__": "cc.Node", "_name": "EquipmentNameLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 429}, {"__id__": 430}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e249oDiOVAULNz1NfhwGGh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1c2sHVh+hC3I4kf+QkYdg5"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "选择装备查看属性", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "5aYdjVWBJGy7F7LJzL4xX+"}, {"__type__": "cc.Node", "_name": "EquipmentStatsLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 432}, {"__id__": 433}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3cVO1uypFIopkWtTMEb0aa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 590.21484375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d2yNEp8JlIea0hzUHCEDNQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "攻击力: +0\\n防御力: +0\\n速度: +0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "67a12wbVZFU5qHGQGXW/5N"}, {"__type__": "cc.Node", "_name": "TotalStatsLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 435}, {"__id__": 436}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -70, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37vq7nt5VKnJiv+DSrbNS7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 434}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 906.85546875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8dhCrNzERGhK91nENO4Kr5"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 434}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "角色总属性:\\n总攻击力: 10\\n总防御力: 5\\n总速度: 8", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "03UmxvSDtOuY5R3U0OWWvh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1fpU4zhotDxIcfKBMtb3S8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eeF+NBNhVN86NRIiLF6s6R"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "41Uq4aEWtKFLzmel1mQzPi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 50, "g": 50, "b": 50, "a": 200}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "be+cwCaKlAVaHj4AQmtJNH"}, {"__type__": "5728egAhq1NQ69MYSpkeYpg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": null, "_panelType": "dialog", "_enableShowAnimation": true, "_enableHideAnimation": true, "_animationDuration": 0.3, "equipmentSlotsContainer": {"__id__": 351}, "equipmentInventoryContainer": {"__id__": 395}, "equipmentStatsPanel": {"__id__": 427}, "equipmentNameLabel": {"__id__": 430}, "equipmentStatsLabel": {"__id__": 433}, "_id": "d5/zq8VW9AoKvQ0rLO4IO+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9c6eVvOPdAFr9CeQkVDnPa"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "e5WM3czaZLfbxFiPVsggnP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 720, "_originalHeight": 1280, "_alignMode": 2, "_lockFlags": 0, "_id": "3cevYAYbxF2IYRhZCrfnWs"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "5187d0c2-2edf-48da-91bd-597f4413becc", "instance": null, "targetOverrides": null}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 447}, "shadows": {"__id__": 448}, "_skybox": {"__id__": 449}, "fog": {"__id__": 450}, "octree": {"__id__": 451}, "skin": {"__id__": 452}, "lightProbeInfo": {"__id__": 453}, "postSettings": {"__id__": 454}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 76}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": true, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]
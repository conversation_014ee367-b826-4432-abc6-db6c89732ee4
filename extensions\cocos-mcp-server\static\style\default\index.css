.mcp-server-panel {
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100%;
    max-height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
}

header h2 {
    margin: 0 0 20px 0;
    font-size: 18px;
}

/* Vue3 应用样式 */
.mcp-app {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* 选项卡导航样式 */
.tab-navigation {
    display: flex;
    border-bottom: 1px solid var(--color-normal-border);
    margin-bottom: 20px;
    background: var(--color-panel);
}

.tab-button {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    color: var(--color-normal-text);
    font-size: 14px;
    transition: all 0.2s ease;
    border-radius: 4px 4px 0 0;
}

.tab-button:hover {
    background: var(--color-normal-fill-emphasis);
}

.tab-button.active {
    border-bottom-color: var(--color-primary);
    color: var(--color-primary);
    font-weight: 600;
    background: var(--color-panel);
}

/* 选项卡内容样式 */
.tab-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

section {
    margin-bottom: 20px;
    padding: 15px;
    background: var(--color-normal-fill-emphasis);
    border-radius: 4px;
}

section h3 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
}

.status-value {
    font-weight: 600;
}

.status-value.running {
    color: var(--color-success-fill);
}

.status-value.stopped {
    color: var(--color-warn-fill);
}

.server-controls {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.server-controls ui-button {
    min-width: 150px;
}

.connection-details {
    margin-top: 10px;
}

footer {
    margin-top: auto;
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
}

ui-prop {
    margin-bottom: 10px;
}

/* 工具管理器样式 */
.tool-manager {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tool-manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-manager-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.config-selector-section {
    margin-bottom: 20px;
}

.tools-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.tools-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--color-normal-border);
    flex-wrap: wrap;
    gap: 10px;
}

.tools-section-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.tools-section-title h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.tools-stats {
    font-size: 12px;
    color: var(--color-normal-text-secondary);
}

.tools-section-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.tools-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
    height: calc(100vh - 400px);
    min-height: 400px;
}

.tools-container::-webkit-scrollbar {
    width: 6px;
}

.tools-container::-webkit-scrollbar-track {
    background: var(--color-normal-fill-emphasis);
    border-radius: 3px;
}

.tools-container::-webkit-scrollbar-thumb {
    background: var(--color-normal-border);
    border-radius: 3px;
}

.tools-container::-webkit-scrollbar-thumb:hover {
    background: var(--color-normal-text-secondary);
}

.tool-category {
    margin-bottom: 20px;
    background: var(--color-normal-fill);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(135deg, var(--color-primary-fill) 0%, var(--color-primary-fill-emphasis) 100%);
    color: var(--color-primary-text);
    font-weight: 600;
}

.category-name {
    font-size: 14px;
}

.category-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
}

.tool-list {
    padding: 10px 15px;
}

.tool-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--color-normal-border);
}

.tool-item:last-child {
    border-bottom: none;
}

.tool-info {
    flex: 1;
    min-width: 0;
    margin-right: 10px;
}

.tool-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--color-normal-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tool-description {
    font-size: 11px;
    color: var(--color-normal-text-secondary);
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.tool-toggle {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.tool-manager-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--color-normal-border);
}

.config-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--color-normal-fill);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    min-width: 400px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid var(--color-normal-border);
    padding-bottom: 15px;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--color-normal-text-secondary);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.modal-close:hover {
    background: var(--color-normal-fill-emphasis);
    color: var(--color-normal-text);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 0 20px 20px 20px;
    border-top: 1px solid var(--color-normal-border);
    padding-top: 15px;
}

/* 按钮样式 */
ui-button.small {
    font-size: 12px;
    padding: 4px 8px;
    min-width: auto;
}

ui-button.secondary {
    background: var(--color-normal-fill-emphasis);
    color: var(--color-normal-text);
    border: 1px solid var(--color-normal-border);
}

ui-button.secondary:hover {
    background: var(--color-normal-fill);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--color-normal-text-secondary);
}

.empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mcp-server-panel {
        padding: 10px;
    }
    
    .tool-manager-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tool-manager-controls {
        justify-content: center;
    }
    
    .tools-section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tools-section-controls {
        justify-content: center;
    }
    
    .tools-container {
        height: calc(100vh - 350px);
    }
    
    .config-actions {
        flex-direction: column;
        align-items: stretch;
    }
}
/**
 * 技能槽组件
 * 单个技能槽的显示和交互，包括冷却动画、状态更新等
 */

import { _decorator, Node, Sprite, Label, ProgressBar, Button, tween, Vec3, Color } from 'cc';
import { BaseUIComponent, ComponentState, IComponentData } from '../base/BaseUIComponent';
import { IPlayerSkill } from '../panels/SkillPanel';

const { ccclass, property } = _decorator;

/**
 * 技能槽数据接口
 */
export interface ISkillSlotData extends IComponentData {
    /** 绑定的技能 */
    skill: IPlayerSkill | null;
    
    /** 槽位索引 */
    slotIndex: number;
    
    /** 是否可用 */
    available: boolean;
    
    /** 冷却结束时间 */
    cooldownEndTime: number;
    
    /** 使用次数 */
    useCount: number;
    
    /** 显示配置 */
    displayConfig: ISlotDisplayConfig;
}

/**
 * 槽位显示配置接口
 */
export interface ISlotDisplayConfig {
    /** 是否显示技能名称 */
    showName: boolean;
    
    /** 是否显示技能等级 */
    showLevel: boolean;
    
    /** 是否显示冷却时间 */
    showCooldown: boolean;
    
    /** 是否显示使用次数 */
    showUseCount: boolean;
    
    /** 是否启用点击效果 */
    enableClickEffect: boolean;
    
    /** 是否启用冷却动画 */
    enableCooldownAnimation: boolean;
}

/**
 * 技能槽状态枚举
 */
export enum SkillSlotState {
    Empty = 'empty',           // 空槽位
    Ready = 'ready',           // 准备就绪
    Cooldown = 'cooldown',     // 冷却中
    Disabled = 'disabled'      // 禁用
}

@ccclass('SkillSlot')
export class SkillSlot extends BaseUIComponent {
    
    @property({ type: Sprite, tooltip: '技能图标' })
    public skillIcon: Sprite | null = null;
    
    @property({ type: Label, tooltip: '技能名称标签' })
    public skillNameLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '技能等级标签' })
    public skillLevelLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '冷却时间标签' })
    public cooldownLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '使用次数标签' })
    public useCountLabel: Label | null = null;
    
    @property({ type: ProgressBar, tooltip: '冷却进度条' })
    public cooldownProgressBar: ProgressBar | null = null;
    
    @property({ type: Button, tooltip: '槽位按钮' })
    public slotButton: Button | null = null;
    
    @property({ type: Node, tooltip: '冷却遮罩' })
    public cooldownMask: Node | null = null;
    
    @property({ type: Node, tooltip: '禁用遮罩' })
    public disabledMask: Node | null = null;
    
    @property({ tooltip: '点击缩放比例' })
    public clickScaleRatio: number = 0.95;
    
    @property({ tooltip: '点击动画时长' })
    public clickAnimationDuration: number = 0.1;
    
    // 技能槽数据
    private _slotData: ISkillSlotData = {
        skill: null,
        slotIndex: 0,
        available: true,
        cooldownEndTime: 0,
        useCount: 0,
        displayConfig: {
            showName: true,
            showLevel: true,
            showCooldown: true,
            showUseCount: false,
            enableClickEffect: true,
            enableCooldownAnimation: true
        }
    };
    
    // 技能槽状态
    private _slotState: SkillSlotState = SkillSlotState.Empty;
    
    // 冷却更新定时器
    private _cooldownUpdateTimer: number = 0;
    
    // 原始颜色
    private _originalIconColor: Color = Color.WHITE.clone();

    protected onComponentLoad(): void {
        console.log(`🎯 技能槽组件加载: ${this.componentId}`);
        
        // 查找UI组件
        this.findSlotComponents();
        
        // 初始化槽位数据
        this.initializeSlotData();
        
        // 绑定按钮事件
        this.bindButtonEvents();
    }

    protected onComponentEnable(): void {
        // 启动冷却更新
        this.startCooldownUpdate();
        
        console.log(`🎯 技能槽组件启用: ${this.componentId}`);
    }

    protected onComponentDisable(): void {
        // 停止冷却更新
        this.stopCooldownUpdate();
        
        console.log(`🎯 技能槽组件禁用: ${this.componentId}`);
    }

    protected onUpdate(): void {
        // 更新冷却状态
        this.updateCooldownState();
    }

    protected onDataChanged(newData: IComponentData, oldData: IComponentData): void {
        const slotData = newData as ISkillSlotData;
        
        if (slotData.skill !== undefined) {
            this.updateSkillDisplay(slotData.skill);
        }
        
        if (slotData.available !== undefined) {
            this.updateAvailableState(slotData.available);
        }
        
        if (slotData.displayConfig) {
            this.applyDisplayConfig(slotData.displayConfig);
        }
        
        if (slotData.useCount !== undefined) {
            this.updateUseCountDisplay(slotData.useCount);
        }
    }

    /**
     * 查找槽位组件
     */
    private findSlotComponents(): void {
        // 查找技能图标
        if (!this.skillIcon) {
            const iconNode = this.node.getChildByName('Icon');
            if (iconNode) {
                this.skillIcon = iconNode.getComponent(Sprite);
            }
        }
        
        // 查找技能名称标签
        if (!this.skillNameLabel) {
            const nameNode = this.node.getChildByName('NameLabel');
            if (nameNode) {
                this.skillNameLabel = nameNode.getComponent(Label);
            }
        }
        
        // 查找技能等级标签
        if (!this.skillLevelLabel) {
            const levelNode = this.node.getChildByName('LevelLabel');
            if (levelNode) {
                this.skillLevelLabel = levelNode.getComponent(Label);
            }
        }
        
        // 查找冷却时间标签
        if (!this.cooldownLabel) {
            const cooldownNode = this.node.getChildByName('CooldownLabel');
            if (cooldownNode) {
                this.cooldownLabel = cooldownNode.getComponent(Label);
            }
        }
        
        // 查找使用次数标签
        if (!this.useCountLabel) {
            const useCountNode = this.node.getChildByName('UseCountLabel');
            if (useCountNode) {
                this.useCountLabel = useCountNode.getComponent(Label);
            }
        }
        
        // 查找冷却进度条
        if (!this.cooldownProgressBar) {
            const progressNode = this.node.getChildByName('CooldownProgress');
            if (progressNode) {
                this.cooldownProgressBar = progressNode.getComponent(ProgressBar);
            }
        }
        
        // 查找槽位按钮
        if (!this.slotButton) {
            this.slotButton = this.node.getComponent(Button);
        }
        
        // 查找冷却遮罩
        if (!this.cooldownMask) {
            this.cooldownMask = this.node.getChildByName('CooldownMask');
        }
        
        // 查找禁用遮罩
        if (!this.disabledMask) {
            this.disabledMask = this.node.getChildByName('DisabledMask');
        }
        
        // 保存原始图标颜色
        if (this.skillIcon) {
            this._originalIconColor = this.skillIcon.color.clone();
        }
    }

    /**
     * 初始化槽位数据
     */
    private initializeSlotData(): void {
        this.setComponentData(this._slotData);
        this.updateSlotState();
    }

    /**
     * 绑定按钮事件
     */
    private bindButtonEvents(): void {
        if (this.slotButton) {
            this.slotButton.node.on(Button.EventType.CLICK, this.onSlotClick, this);
        }
        
        // 绑定触摸事件用于点击效果
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    /**
     * 启动冷却更新
     */
    private startCooldownUpdate(): void {
        if (this._cooldownUpdateTimer) return;
        
        this._cooldownUpdateTimer = setInterval(() => {
            this.updateCooldownDisplay();
        }, 100); // 每100ms更新一次
    }

    /**
     * 停止冷却更新
     */
    private stopCooldownUpdate(): void {
        if (this._cooldownUpdateTimer) {
            clearInterval(this._cooldownUpdateTimer);
            this._cooldownUpdateTimer = 0;
        }
    }

    /**
     * 更新冷却状态
     */
    private updateCooldownState(): void {
        if (!this._slotData.skill) {
            this.setSlotState(SkillSlotState.Empty);
            return;
        }
        
        if (!this._slotData.available) {
            this.setSlotState(SkillSlotState.Disabled);
            return;
        }
        
        const currentTime = Date.now();
        if (currentTime < this._slotData.cooldownEndTime) {
            this.setSlotState(SkillSlotState.Cooldown);
        } else {
            this.setSlotState(SkillSlotState.Ready);
        }
    }

    /**
     * 设置槽位状态
     */
    private setSlotState(state: SkillSlotState): void {
        if (this._slotState === state) return;
        
        this._slotState = state;
        this.updateSlotAppearance();
    }

    /**
     * 更新槽位外观
     */
    private updateSlotAppearance(): void {
        switch (this._slotState) {
            case SkillSlotState.Empty:
                this.showEmptyState();
                break;
            case SkillSlotState.Ready:
                this.showReadyState();
                break;
            case SkillSlotState.Cooldown:
                this.showCooldownState();
                break;
            case SkillSlotState.Disabled:
                this.showDisabledState();
                break;
        }
    }

    /**
     * 显示空槽位状态
     */
    private showEmptyState(): void {
        if (this.skillIcon) {
            this.skillIcon.node.active = false;
        }
        
        if (this.skillNameLabel) {
            this.skillNameLabel.node.active = false;
        }
        
        if (this.skillLevelLabel) {
            this.skillLevelLabel.node.active = false;
        }
        
        if (this.cooldownMask) {
            this.cooldownMask.active = false;
        }
        
        if (this.disabledMask) {
            this.disabledMask.active = false;
        }
        
        if (this.slotButton) {
            this.slotButton.interactable = false;
        }
    }

    /**
     * 显示准备就绪状态
     */
    private showReadyState(): void {
        if (this.skillIcon) {
            this.skillIcon.node.active = true;
            this.skillIcon.color = this._originalIconColor;
        }
        
        if (this.cooldownMask) {
            this.cooldownMask.active = false;
        }
        
        if (this.disabledMask) {
            this.disabledMask.active = false;
        }
        
        if (this.slotButton) {
            this.slotButton.interactable = true;
        }
    }

    /**
     * 显示冷却状态
     */
    private showCooldownState(): void {
        if (this.skillIcon) {
            this.skillIcon.color = Color.GRAY;
        }
        
        if (this.cooldownMask) {
            this.cooldownMask.active = true;
        }
        
        if (this.slotButton) {
            this.slotButton.interactable = false;
        }
    }

    /**
     * 显示禁用状态
     */
    private showDisabledState(): void {
        if (this.skillIcon) {
            this.skillIcon.color = Color.GRAY;
        }
        
        if (this.disabledMask) {
            this.disabledMask.active = true;
        }
        
        if (this.slotButton) {
            this.slotButton.interactable = false;
        }
    }

    /**
     * 更新冷却显示
     */
    private updateCooldownDisplay(): void {
        if (!this._slotData.skill || this._slotState !== SkillSlotState.Cooldown) {
            return;
        }
        
        const currentTime = Date.now();
        const remainingTime = Math.max(0, this._slotData.cooldownEndTime - currentTime);
        const totalCooldown = this._slotData.skill.skillData.cooldown * 1000;
        
        // 更新冷却标签
        if (this.cooldownLabel && this._slotData.displayConfig.showCooldown) {
            if (remainingTime > 0) {
                this.cooldownLabel.string = (remainingTime / 1000).toFixed(1);
                this.cooldownLabel.node.active = true;
            } else {
                this.cooldownLabel.node.active = false;
            }
        }
        
        // 更新冷却进度条
        if (this.cooldownProgressBar && this._slotData.displayConfig.enableCooldownAnimation) {
            const progress = 1 - (remainingTime / totalCooldown);
            this.cooldownProgressBar.progress = Math.max(0, Math.min(1, progress));
        }
    }

    /**
     * 更新技能显示
     */
    private updateSkillDisplay(skill: IPlayerSkill | null): void {
        this._slotData.skill = skill;
        
        if (!skill) {
            this.setSlotState(SkillSlotState.Empty);
            return;
        }
        
        // 更新技能图标
        if (this.skillIcon) {
            this.skillIcon.node.active = true;
            // 这里需要加载技能图标
            // this.loadSkillIcon(skill.skillData.icon);
        }
        
        // 更新技能名称
        if (this.skillNameLabel && this._slotData.displayConfig.showName) {
            this.skillNameLabel.string = skill.skillData.name;
            this.skillNameLabel.node.active = true;
        }
        
        // 更新技能等级
        if (this.skillLevelLabel && this._slotData.displayConfig.showLevel) {
            this.skillLevelLabel.string = `Lv.${skill.level}`;
            this.skillLevelLabel.node.active = true;
        }
        
        this.updateSlotState();
    }

    /**
     * 更新可用状态
     */
    private updateAvailableState(available: boolean): void {
        this._slotData.available = available;
        this.updateSlotState();
    }

    /**
     * 更新使用次数显示
     */
    private updateUseCountDisplay(useCount: number): void {
        this._slotData.useCount = useCount;
        
        if (this.useCountLabel && this._slotData.displayConfig.showUseCount) {
            this.useCountLabel.string = useCount.toString();
            this.useCountLabel.node.active = true;
        }
    }

    /**
     * 应用显示配置
     */
    private applyDisplayConfig(config: ISlotDisplayConfig): void {
        this._slotData.displayConfig = { ...this._slotData.displayConfig, ...config };
        
        // 更新各个显示元素的可见性
        if (this.skillNameLabel) {
            this.skillNameLabel.node.active = config.showName && this._slotData.skill !== null;
        }
        
        if (this.skillLevelLabel) {
            this.skillLevelLabel.node.active = config.showLevel && this._slotData.skill !== null;
        }
        
        if (this.cooldownLabel) {
            this.cooldownLabel.node.active = config.showCooldown && this._slotState === SkillSlotState.Cooldown;
        }
        
        if (this.useCountLabel) {
            this.useCountLabel.node.active = config.showUseCount && this._slotData.skill !== null;
        }
        
        if (this.cooldownProgressBar) {
            this.cooldownProgressBar.node.active = config.enableCooldownAnimation;
        }
    }

    /**
     * 槽位点击事件
     */
    private onSlotClick(): void {
        if (this._slotState !== SkillSlotState.Ready) {
            return;
        }
        
        console.log(`🎯 技能槽点击: ${this._slotData.skill?.skillData.name || '空'}`);
        
        // 播放点击效果
        if (this._slotData.displayConfig.enableClickEffect) {
            this.playClickEffect();
        }
        
        // 发送点击事件
        this.emitComponentEvent('slot_clicked', {
            slotIndex: this._slotData.slotIndex,
            skill: this._slotData.skill
        });
    }

    /**
     * 触摸开始事件
     */
    private onTouchStart(): void {
        if (this._slotData.displayConfig.enableClickEffect && this._slotState === SkillSlotState.Ready) {
            this.node.setScale(this.clickScaleRatio, this.clickScaleRatio, 1);
        }
    }

    /**
     * 触摸结束事件
     */
    private onTouchEnd(): void {
        if (this._slotData.displayConfig.enableClickEffect) {
            this.node.setScale(1, 1, 1);
        }
    }

    /**
     * 触摸取消事件
     */
    private onTouchCancel(): void {
        if (this._slotData.displayConfig.enableClickEffect) {
            this.node.setScale(1, 1, 1);
        }
    }

    /**
     * 播放点击效果
     */
    private playClickEffect(): void {
        tween(this.node)
            .to(this.clickAnimationDuration / 2, { scale: new Vec3(this.clickScaleRatio, this.clickScaleRatio, 1) })
            .to(this.clickAnimationDuration / 2, { scale: new Vec3(1, 1, 1) })
            .start();
    }

    // ==================== 公共API ====================

    /**
     * 更新技能
     */
    public updateSkill(skill: IPlayerSkill | null): void {
        this.setDataValue('skill', skill);
    }

    /**
     * 设置槽位索引
     */
    public setSlotIndex(index: number): void {
        this._slotData.slotIndex = index;
        this.setDataValue('slotIndex', index);
    }

    /**
     * 设置冷却结束时间
     */
    public setCooldownEndTime(endTime: number): void {
        this._slotData.cooldownEndTime = endTime;
        this.setDataValue('cooldownEndTime', endTime);
    }

    /**
     * 设置可用状态
     */
    public setAvailable(available: boolean): void {
        this.setDataValue('available', available);
    }

    /**
     * 增加使用次数
     */
    public incrementUseCount(): void {
        this._slotData.useCount++;
        this.setDataValue('useCount', this._slotData.useCount);
    }

    /**
     * 更新显示配置
     */
    public updateDisplayConfig(config: Partial<ISlotDisplayConfig>): void {
        const newConfig = { ...this._slotData.displayConfig, ...config };
        this.setDataValue('displayConfig', newConfig);
    }

    /**
     * 获取槽位状态
     */
    public getSlotState(): SkillSlotState {
        return this._slotState;
    }

    /**
     * 获取绑定的技能
     */
    public getSkill(): IPlayerSkill | null {
        return this._slotData.skill;
    }

    /**
     * 检查是否为空槽位
     */
    public isEmpty(): boolean {
        return this._slotState === SkillSlotState.Empty;
    }

    /**
     * 检查是否准备就绪
     */
    public isReady(): boolean {
        return this._slotState === SkillSlotState.Ready;
    }

    /**
     * 检查是否在冷却中
     */
    public isCooldown(): boolean {
        return this._slotState === SkillSlotState.Cooldown;
    }
}

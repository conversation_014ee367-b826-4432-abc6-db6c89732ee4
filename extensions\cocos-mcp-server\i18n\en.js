"use strict";

module.exports = {
    "extension_name": "Cocos MCP Server",
    "description": "AI MCP Server for Cocos Creator 3.8",
    "panel_title": "MCP Server",
    "open_panel": "Open MCP Panel",
    "start_server": "Start Server",
    "stop_server": "Stop Server",
    "server_status": "Server Status",
    "port": "Port",
    "settings": "Settings",
    "connected": "Connected",
    "disconnected": "Disconnected",
    "server_running": "Server is running on port {0}",
    "server_stopped": "Server has stopped",
    "auto_start": "Auto Start",
    "debug_log": "Debug Logging",
    "max_connections": "Max Connections",
    "connection_info": "Connection Info",
    "http_url": "HTTP URL",
    "copy": "Copy",
    "save_settings": "Save Settings",
    "settings_saved": "Settings saved successfully",
    "server_started": "MCP Server Started",
    "server_stopped_msg": "MCP Server Stopped",
    "failed_to_start": "Failed to start server",
    "failed_to_stop": "Failed to stop server",
    "failed_to_save": "Failed to save settings",
    "url_copied": "HTTP URL copied to clipboard",
    "tool_manager": "Tool Manager",
    "open_tool_manager": "Open Tool Manager",
    "create_config": "Create Configuration",
    "edit_config": "Edit Configuration",
    "delete_config": "Delete Configuration",
    "import_config": "Import Configuration",
    "export_config": "Export Configuration",
    "apply_config": "Apply Configuration",
    "select_all": "Select All",
    "deselect_all": "Deselect All",
    "save_changes": "Save Changes",
    "config_name": "Configuration Name",
    "config_description": "Configuration Description",
    "current_config": "Current Configuration",
    "tool_management": "Tool Management",
    "total_tools": "Total Tools",
    "enabled_tools": "Enabled",
    "disabled_tools": "Disabled",
    "no_config_selected": "No Configuration Selected",
    "select_config_first": "Please select a configuration or create a new one first",
    "config_created": "Configuration created successfully",
    "config_updated": "Configuration updated successfully",
    "config_deleted": "Configuration deleted successfully",
    "config_applied": "Configuration applied successfully",
    "config_exported": "Configuration exported successfully",
    "config_imported": "Configuration imported successfully",
    "confirm_delete": "Confirm Delete",
    "delete_config_confirm": "Are you sure you want to delete configuration \"{0}\"? This action cannot be undone.",
    "max_config_slots_reached": "Maximum configuration slots reached ({0})",
    "invalid_config_format": "Invalid configuration format",
    "invalid_json_format": "Invalid JSON format or configuration structure",
    "server_tab": "Server",
    "tools_tab": "Tool Management",
    "available_tools": "Available Tools",
    "scene_tools": "Scene Tools",
    "node_tools": "Node Tools",
    "component_tools": "Component Tools",
    "prefab_tools": "Prefab Tools",
    "project_tools": "Project Tools",
    "debug_tools": "Debug Tools",
    "preferences_tools": "Preferences",
    "server_tools": "Server Tools",
    "broadcast_tools": "Broadcast Tools",
    "scene_advanced_tools": "Advanced Scene Tools",
    "scene_view_tools": "Scene View Tools",
    "reference_image_tools": "Reference Image Tools",
    "asset_advanced_tools": "Advanced Asset Tools",
    "validation_tools": "Validation Tools"
};
/**
 * 底部面板控制器
 * 管理底部按钮点击事件和界面打开
 */

import { _decorator, Component, Node, Button, Prefab, instantiate, Canvas, assetManager, Widget, UITransform } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('BottomPanelController')
export class BottomPanelController extends Component {

    // 预制件UUID配置
    private readonly PREFAB_UUIDS = {
        CHARACTER_PANEL: '0a599793-7399-4029-8b73-7bf8262cb763',  // CharacterPanel.prefab
        BAG_PANEL: '',        // 背包界面UUID（待配置）
        SHOP_PANEL: '',       // 商店界面UUID（待配置）
        SETTINGS_PANEL: ''    // 设置界面UUID（待配置）
    };

    // 当前打开的界面
    private currentPanel: Node | null = null;

    start() {
        console.log('🎮 底部面板控制器开始初始化...');

        // 延迟绑定事件，确保节点完全加载
        this.scheduleOnce(() => {
            this.bindButtonEvents();
            console.log('🎮 底部面板控制器初始化完成');
        }, 0.1);
    }

    onDestroy() {
        this.unbindButtonEvents();
    }

    /**
     * 绑定按钮事件
     */
    private bindButtonEvents(): void {
        // 查找User按钮
        const userButton = this.findButton('User');
        if (userButton) {
            userButton.node.on(Button.EventType.CLICK, this.onUserButtonClick, this);
            console.log('✅ User按钮事件绑定成功');
        }

        // 查找Bag按钮
        const bagButton = this.findButton('Bag');
        if (bagButton) {
            bagButton.node.on(Button.EventType.CLICK, this.onBagButtonClick, this);
            console.log('✅ Bag按钮事件绑定成功');
        }

        // 查找Shop按钮
        const shopButton = this.findButton('Shop');
        if (shopButton) {
            shopButton.node.on(Button.EventType.CLICK, this.onShopButtonClick, this);
            console.log('✅ Shop按钮事件绑定成功');
        }

        // 查找Map按钮
        const mapButton = this.findButton('Map');
        if (mapButton) {
            mapButton.node.on(Button.EventType.CLICK, this.onMapButtonClick, this);
            console.log('✅ Map按钮事件绑定成功');
        }

        // 查找Else按钮
        const elseButton = this.findButton('Else');
        if (elseButton) {
            elseButton.node.on(Button.EventType.CLICK, this.onElseButtonClick, this);
            console.log('✅ Else按钮事件绑定成功');
        }
    }

    /**
     * 解绑按钮事件
     */
    private unbindButtonEvents(): void {
        const userButton = this.findButton('User');
        if (userButton) {
            userButton.node.off(Button.EventType.CLICK, this.onUserButtonClick, this);
        }

        const bagButton = this.findButton('Bag');
        if (bagButton) {
            bagButton.node.off(Button.EventType.CLICK, this.onBagButtonClick, this);
        }

        const shopButton = this.findButton('Shop');
        if (shopButton) {
            shopButton.node.off(Button.EventType.CLICK, this.onShopButtonClick, this);
        }

        const mapButton = this.findButton('Map');
        if (mapButton) {
            mapButton.node.off(Button.EventType.CLICK, this.onMapButtonClick, this);
        }

        const elseButton = this.findButton('Else');
        if (elseButton) {
            elseButton.node.off(Button.EventType.CLICK, this.onElseButtonClick, this);
        }
    }

    /**
     * 查找指定名称的按钮
     */
    private findButton(buttonName: string): Button | null {
        console.log(`🔍 查找按钮: ${buttonName}`);

        const buttonPath = `BottonIcon/${buttonName}/Button`;
        console.log(`🔍 按钮路径: ${buttonPath}`);

        const buttonNode = this.node.getChildByPath(buttonPath);
        console.log(`🔍 找到按钮节点:`, buttonNode ? buttonNode.name : 'null');

        if (buttonNode) {
            const buttonComponent = buttonNode.getComponent(Button);
            console.log(`🔍 按钮组件:`, buttonComponent ? '存在' : '不存在');
            return buttonComponent;
        }

        console.warn(`❌ 找不到按钮: ${buttonName}`);

        // 尝试打印当前节点的子节点结构
        console.log('🔍 当前节点子节点:');
        this.printNodeChildren(this.node, 0, 2);

        return null;
    }

    /**
     * 打印节点子节点结构（调试用）
     */
    private printNodeChildren(node: Node, depth: number, maxDepth: number): void {
        if (depth > maxDepth) return;

        const indent = '  '.repeat(depth);
        console.log(`${indent}- ${node.name}`);

        for (const child of node.children) {
            this.printNodeChildren(child, depth + 1, maxDepth);
        }
    }

    /**
     * User按钮点击事件
     */
    private onUserButtonClick(): void {
        console.log('🎯 点击User按钮 - 打开角色界面');
        this.openCharacterPanel();
    }

    /**
     * Bag按钮点击事件
     */
    private onBagButtonClick(): void {
        console.log('🎯 点击Bag按钮 - 打开背包界面');
        if (this.PREFAB_UUIDS.BAG_PANEL) {
            this.loadPrefabByUUID(this.PREFAB_UUIDS.BAG_PANEL, '背包界面');
        } else {
            console.warn('⚠️ 背包界面UUID未配置，请先配置PREFAB_UUIDS.BAG_PANEL');
        }
    }

    /**
     * Shop按钮点击事件
     */
    private onShopButtonClick(): void {
        console.log('🎯 点击Shop按钮 - 打开商店界面');
        if (this.PREFAB_UUIDS.SHOP_PANEL) {
            this.loadPrefabByUUID(this.PREFAB_UUIDS.SHOP_PANEL, '商店界面');
        } else {
            console.warn('⚠️ 商店界面UUID未配置，请先配置PREFAB_UUIDS.SHOP_PANEL');
        }
    }

    /**
     * Map按钮点击事件
     */
    private onMapButtonClick(): void {
        console.log('🎯 点击Map按钮 - 返回主界面');
        this.returnToMainInterface();
    }

    /**
     * Else按钮点击事件
     */
    private onElseButtonClick(): void {
        console.log('🎯 点击Else按钮 - 打开设置界面');
        if (this.PREFAB_UUIDS.SETTINGS_PANEL) {
            this.loadPrefabByUUID(this.PREFAB_UUIDS.SETTINGS_PANEL, '设置界面');
        } else {
            console.warn('⚠️ 设置界面UUID未配置，请先配置PREFAB_UUIDS.SETTINGS_PANEL');
        }
    }

    /**
     * 打开角色界面
     */
    private openCharacterPanel(): void {
        console.log('🎯 打开角色界面');
        this.loadPrefabByUUID(this.PREFAB_UUIDS.CHARACTER_PANEL, '角色界面');
    }

    /**
     * 通用的UUID预制件加载方法
     */
    private loadPrefabByUUID(uuid: string, panelName: string): void {
        if (!uuid) {
            console.error(`❌ ${panelName}的UUID未配置`);
            return;
        }

        // 关闭当前界面
        this.closeCurrentPanel();

        console.log(`🔄 加载${panelName}预制件，UUID: ${uuid}`);

        assetManager.loadAny({ uuid: uuid }, (err, asset) => {
            if (err) {
                console.error(`❌ 加载${panelName}失败:`, err);
                return;
            }

            if (asset instanceof Prefab) {
                console.log(`✅ ${panelName}加载成功`);
                this.createPanelFromPrefab(asset, panelName);
            } else {
                console.error(`❌ 加载的资源不是预制件类型: ${typeof asset}`);
            }
        });
    }

    /**
     * 从预制件创建面板
     */
    private createPanelFromPrefab(prefab: Prefab, panelName: string = '界面'): void {
        // 实例化预制件
        const panelNode = instantiate(prefab);

        // 添加到Canvas下
        let canvasNode = this.node;
        let foundCanvas = false;

        // 先检查当前节点是否是Canvas
        if (canvasNode.getComponent(Canvas)) {
            panelNode.setParent(canvasNode);
            console.log(`✅ ${panelName}已添加到Canvas (当前节点)`);
            foundCanvas = true;
        } else {
            // 向上查找Canvas节点
            while (canvasNode.parent) {
                canvasNode = canvasNode.parent;
                const canvas = canvasNode.getComponent(Canvas);
                if (canvas) {
                    panelNode.setParent(canvasNode);
                    console.log(`✅ ${panelName}已添加到Canvas`);
                    foundCanvas = true;
                    break;
                }
            }
        }

        // 如果没找到Canvas，添加到当前节点的父节点
        if (!foundCanvas) {
            panelNode.setParent(this.node.parent);
            console.log(`⚠️ 未找到Canvas，${panelName}添加到父节点`);
        }

        // 动态适配底部功能栏高度
        this.adaptPanelToBottomBar(panelNode, panelName);

        // 设置为最上层
        panelNode.setSiblingIndex(-1);

        // 记录当前界面
        this.currentPanel = panelNode;

        // 绑定关闭事件
        this.bindPanelCloseEvents(panelNode);

        console.log(`✅ ${panelName}打开成功`);
    }

    /**
     * 动态适配面板到底部功能栏
     */
    private adaptPanelToBottomBar(panelNode: Node, panelName: string): void {
        const widget = panelNode.getComponent(Widget);
        if (!widget) {
            console.log(`⚠️ ${panelName}没有Widget组件，跳过适配`);
            return;
        }

        // 获取Canvas尺寸
        const canvas = panelNode.parent?.getComponent(Canvas);
        if (!canvas) {
            console.log(`⚠️ 无法获取Canvas，跳过适配`);
            return;
        }

        const canvasTransform = panelNode.parent?.getComponent(UITransform);
        if (!canvasTransform) {
            console.log(`⚠️ 无法获取Canvas UITransform，跳过适配`);
            return;
        }

        // 获取底部功能栏的实际高度和位置
        const bottomPanelTransform = this.node.getComponent(UITransform);
        if (!bottomPanelTransform) {
            console.log(`⚠️ 无法获取BottomPanel UITransform，跳过适配`);
            return;
        }

        // 计算底部功能栏占屏幕高度的百分比
        const canvasHeight = canvasTransform.contentSize.height;
        const bottomPanelHeight = bottomPanelTransform.contentSize.height;

        // 添加额外的安全边距（20像素）
        const safeMargin = 20;
        const totalBottomSpace = bottomPanelHeight + safeMargin;

        // 计算百分比（相对于Canvas高度）
        const bottomPercentage = totalBottomSpace / canvasHeight;

        // 设置Widget的底部边距为百分比模式
        widget.isAbsoluteBottom = false;
        widget.bottom = bottomPercentage;

        console.log(`🎯 ${panelName}适配完成:`);
        console.log(`   Canvas高度: ${canvasHeight}px`);
        console.log(`   底部功能栏高度: ${bottomPanelHeight}px`);
        console.log(`   安全边距: ${safeMargin}px`);
        console.log(`   总底部空间: ${totalBottomSpace}px`);
        console.log(`   底部百分比: ${(bottomPercentage * 100).toFixed(2)}%`);
    }

    /**
     * 绑定界面关闭事件
     */
    private bindPanelCloseEvents(panelNode: Node): void {
        // 方法1: 查找关闭按钮
        this.findAndBindCloseButton(panelNode);
        
        // 方法2: 监听自定义关闭事件
        panelNode.on('panel-close', this.closeCurrentPanel, this);
        
        // 方法3: 点击背景关闭（可选）
        // this.bindBackgroundClose(panelNode);
    }

    /**
     * 查找并绑定关闭按钮
     */
    private findAndBindCloseButton(panelNode: Node): void {
        // 常见的关闭按钮路径
        const closePaths = [
            'CloseButton',
            'Header/CloseButton',
            'TopPanel/CloseButton',
            'Background/CloseButton'
        ];
        
        for (const path of closePaths) {
            const closeNode = panelNode.getChildByPath(path);
            if (closeNode) {
                const closeButton = closeNode.getComponent(Button);
                if (closeButton) {
                    closeButton.node.on(Button.EventType.CLICK, this.closeCurrentPanel, this);
                    console.log(`✅ 绑定关闭按钮: ${path}`);
                    return;
                }
            }
        }
        
        // 如果没找到，尝试查找所有Button组件
        const buttons = panelNode.getComponentsInChildren(Button);
        for (const button of buttons) {
            if (button.node.name.toLowerCase().includes('close') || 
                button.node.name.toLowerCase().includes('back')) {
                button.node.on(Button.EventType.CLICK, this.closeCurrentPanel, this);
                console.log(`✅ 自动绑定关闭按钮: ${button.node.name}`);
                return;
            }
        }
        
        console.warn('⚠️ 未找到关闭按钮，可能需要手动关闭界面');
    }

    /**
     * 关闭当前界面
     */
    private closeCurrentPanel(): void {
        if (this.currentPanel && this.currentPanel.isValid) {
            this.currentPanel.destroy();
            this.currentPanel = null;
            console.log('✅ 界面关闭成功');
        }
    }

    /**
     * 检查是否有界面打开
     */
    public isPanelOpen(): boolean {
        return this.currentPanel !== null && this.currentPanel.isValid;
    }

    /**
     * 强制关闭所有界面
     */
    public closeAllPanels(): void {
        this.closeCurrentPanel();
    }

    /**
     * 获取当前打开的界面
     */
    public getCurrentPanel(): Node | null {
        return this.currentPanel;
    }

    /**
     * 设置预制件UUID（用于配置其他界面）
     */
    public setPrefabUUID(panelType: 'BAG_PANEL' | 'SHOP_PANEL' | 'SETTINGS_PANEL', uuid: string): void {
        (this.PREFAB_UUIDS as any)[panelType] = uuid;
        console.log(`✅ 已设置${panelType}的UUID: ${uuid}`);
    }

    /**
     * 获取预制件UUID配置
     */
    public getPrefabUUIDs(): Readonly<typeof this.PREFAB_UUIDS> {
        return this.PREFAB_UUIDS;
    }

    /**
     * 打印当前UUID配置（调试用）
     */
    public printUUIDConfig(): void {
        console.log('📋 当前预制件UUID配置:');
        console.log('  角色界面:', this.PREFAB_UUIDS.CHARACTER_PANEL);
        console.log('  背包界面:', this.PREFAB_UUIDS.BAG_PANEL || '未配置');
        console.log('  商店界面:', this.PREFAB_UUIDS.SHOP_PANEL || '未配置');
        console.log('  设置界面:', this.PREFAB_UUIDS.SETTINGS_PANEL || '未配置');
    }

    /**
     * 返回主界面
     * 关闭所有打开的界面预制体，显示主界面内容
     */
    private returnToMainInterface(): void {
        console.log('🏠 返回主界面...');

        // 1. 关闭当前打开的预制体界面
        this.closeCurrentPanel();

        // 2. 隐藏场景中的其他界面（如装备界面等）
        this.hideScenePanels();

        // 3. 显示主界面内容
        this.showMainInterfaceContent();

        console.log('✅ 已返回主界面');
    }

    /**
     * 隐藏场景中的其他界面
     */
    private hideScenePanels(): void {
        // 查找并隐藏装备界面
        const equipmentPanel = this.findSceneNode('EquipmentPanel');
        if (equipmentPanel) {
            equipmentPanel.active = false;
            console.log('✅ 隐藏装备界面');
        }

        // 可以在这里添加其他需要隐藏的界面
        // 例如：技能界面、任务界面等

        // 示例：隐藏其他可能的界面
        const panelsToHide = [
            'SkillPanel',
            'QuestPanel',
            'InventoryPanel',
            'SettingsPanel'
        ];

        panelsToHide.forEach(panelName => {
            const panel = this.findSceneNode(panelName);
            if (panel && panel.active) {
                panel.active = false;
                console.log(`✅ 隐藏${panelName}`);
            }
        });
    }

    /**
     * 显示主界面内容
     */
    private showMainInterfaceContent(): void {
        // 确保主界面的核心组件是显示的
        const mainUI = this.findSceneNode('MainUI');
        if (mainUI) {
            mainUI.active = true;
            console.log('✅ 显示主界面UI');
        }

        // 确保地图面板是显示的
        const mapPanel = this.findSceneNode('MapPanel');
        if (mapPanel) {
            mapPanel.active = true;
            console.log('✅ 显示地图面板');
        }

        // 确保行为面板是显示的
        const behaviorPanel = this.findSceneNode('BehaviorPanel');
        if (behaviorPanel) {
            behaviorPanel.active = true;
            console.log('✅ 显示行为面板');
        }

        // 确保顶部面板是显示的
        const topPanel = this.findSceneNode('TopPanel');
        if (topPanel) {
            topPanel.active = true;
            console.log('✅ 显示顶部面板');
        }
    }

    /**
     * 在场景中查找指定名称的节点
     */
    private findSceneNode(nodeName: string): Node | null {
        // 从Canvas开始查找
        let canvasNode = this.node;
        while (canvasNode.parent) {
            const canvas = canvasNode.getComponent(Canvas);
            if (canvas) {
                break;
            }
            canvasNode = canvasNode.parent;
        }

        // 在Canvas下查找指定节点
        return this.findNodeRecursively(canvasNode, nodeName);
    }

    /**
     * 递归查找节点
     */
    private findNodeRecursively(parent: Node, targetName: string): Node | null {
        // 检查当前节点
        if (parent.name === targetName) {
            return parent;
        }

        // 递归检查子节点
        for (const child of parent.children) {
            const found = this.findNodeRecursively(child, targetName);
            if (found) {
                return found;
            }
        }

        return null;
    }
}

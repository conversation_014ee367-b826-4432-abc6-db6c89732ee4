/**
 * 适配测试脚本
 * 用于测试不同分辨率下的UI适配效果
 */

import { _decorator, Component, Node, UITransform, sys, Label } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('AdaptationTest')
export class AdaptationTest extends Component {

    @property(Label)
    infoLabel: Label = null!;

    start() {
        this.displayAdaptationInfo();
    }

    /**
     * 显示适配信息
     */
    private displayAdaptationInfo(): void {
        const canvasTransform = this.node.parent?.getComponent(UITransform);
        if (!canvasTransform) {
            console.log('无法获取Canvas信息');
            return;
        }

        const canvasSize = canvasTransform.contentSize;
        const aspectRatio = canvasSize.width / canvasSize.height;
        
        // 计算适配距离
        let safeBottomDistance: number;
        
        if (canvasSize.height <= 1280) {
            safeBottomDistance = 100;
        } else if (canvasSize.height <= 1920) {
            safeBottomDistance = 120 * (canvasSize.height / 1280);
        } else {
            safeBottomDistance = 150 * (canvasSize.height / 1280);
        }
        
        if (sys.isMobile) {
            safeBottomDistance += 20;
        }
        
        const bottomPercentage = safeBottomDistance / canvasSize.height;
        
        const info = [
            `屏幕信息:`,
            `尺寸: ${canvasSize.width}x${canvasSize.height}`,
            `宽高比: ${aspectRatio.toFixed(2)}`,
            `平台: ${sys.isMobile ? '移动设备' : '桌面设备'}`,
            ``,
            `适配计算:`,
            `安全距离: ${safeBottomDistance.toFixed(1)}px`,
            `底部百分比: ${(bottomPercentage * 100).toFixed(2)}%`,
            ``,
            `设备类型判断:`,
            `${this.getDeviceType(canvasSize, aspectRatio)}`
        ].join('\n');
        
        if (this.infoLabel) {
            this.infoLabel.string = info;
        }
        
        console.log('📱 适配测试信息:');
        console.log(info);
    }

    /**
     * 判断设备类型
     */
    private getDeviceType(canvasSize: any, aspectRatio: number): string {
        if (aspectRatio > 1) {
            return '横屏设备';
        }
        
        if (canvasSize.height >= 2400) {
            return '超长屏设备 (21:9或更长)';
        } else if (canvasSize.height >= 1920) {
            return '长屏设备 (18:9左右)';
        } else if (canvasSize.height >= 1280) {
            return '标准屏设备 (16:9左右)';
        } else {
            return '小屏设备';
        }
    }
}

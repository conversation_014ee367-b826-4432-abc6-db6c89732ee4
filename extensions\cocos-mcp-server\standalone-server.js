const { MCPServer } = require('./dist/mcp-server.js');

// Mock Editor object for standalone mode
global.Editor = {
    Project: {
        path: process.cwd()
    }
};

// Default settings for standalone mode
const settings = {
    port: 3000,
    autoStart: true,
    enableDebugLog: true,
    allowedOrigins: ['*'],
    maxConnections: 10
};

console.log('[Standalone MCP Server] Starting server...');

const server = new MCPServer(settings);

server.start().then(() => {
    console.log('[Standalone MCP Server] Server started successfully');
}).catch(error => {
    console.error('[Standalone MCP Server] Failed to start server:', error);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('[Standalone MCP Server] Received SIGTERM, shutting down...');
    server.stop();
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('[Standalone MCP Server] Received SIGINT, shutting down...');
    server.stop();
    process.exit(0);
});
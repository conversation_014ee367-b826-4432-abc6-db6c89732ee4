[{"__type__": "cc.SceneAsset", "_name": "Battle", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "Battle", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 119}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 120}, "_id": "8cb532e6-3f16-46a1-abb6-86cbe350c139"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 14}, {"__id__": 22}, {"__id__": 43}], "_active": true, "_components": [{"__id__": 114}, {"__id__": 115}, {"__id__": 116}, {"__id__": 117}, {"__id__": 118}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5ehzkVAFJBO5GwI7+PljjA"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7fMcYLJqBPoYcwlMIx/Ojd"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "c6d6mwOyxFeYNtiyJFNfxW"}, {"__type__": "cc.Node", "_name": "TopInfoPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 12}, {"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 580, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5bT+7x/5dAMJxgJa6BM6en"}, {"__type__": "cc.Node", "_name": "RoundLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 7}, {"__id__": 8}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -100, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "539R7r4gFIV64f3pe9lQpg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 87.130859375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "benjfWYMtDy40X/GbJK4gx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "回合: 1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "a8Y+5kpz5C9LYOODWjhjbq"}, {"__type__": "cc.Node", "_name": "TimeLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 100, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "92VtLERuhAcIsHbnMF3N/K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 121.39453125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e5u0nfqkZIz44uXazzU4m3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "时间: 00:01", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "38pn09IK5MAboEEfhgFfbx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3eQxSThT9FxbA1EfrzK677"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 50, "g": 50, "b": 80, "a": 200}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8ep5FYBLBCM4zTAC6h2mH6"}, {"__type__": "cc.Node", "_name": "RetreatButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 15}], "_active": true, "_components": [{"__id__": 18}, {"__id__": 19}, {"__id__": 21}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -260, "y": 580, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e5yoWRA+BMG4Y7Fq+P6y60"}, {"__type__": "cc.Node", "_name": "RetreatLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2a4w70K9JE54OBDmsQI3Ht"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9Sb7v2/BFdZIV+BAPZDYi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "撤退", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2aCPuwE7dFILqUzQ9p3PdG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "84l1X5KYlPGKLFfBcLmw30"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 20}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "61nvZQVENDwpdqEAGyZHM7"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "4ff39kIvYROD71KoejCItzA", "handler": "onRetreatButtonClicked", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 255}, "_spriteFrame": {"__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "83YvJVJBdHSaZ37ojFJA8L"}, {"__type__": "cc.Node", "_name": "PlayerArea", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 23}], "_active": true, "_components": [{"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -260, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "facRa9ZchBHrhwzzcv3h1j"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 22}, "_prefab": {"__id__": 24}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 23}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 25}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "34YmrlSqdIcq5CMIb1NeXN", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [{"__id__": 26}], "propertyOverrides": [{"__id__": 29}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}, {"__id__": 40}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 27}, "components": [{"__id__": 28}]}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "d6382ANNPBDjY230JxrBmt5", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 23}}, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "nameLabel": null, "hpLabel": null, "mpLabel": null, "actionLabel": null, "hpBar": null, "mpBar": null, "actionBar": null, "characterImage": null, "_id": "7ahmMkvjdAD4gJAWyWozXc"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 200, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 41}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e4tCDyHoNDN4TOjk12toX5"}, {"__type__": "cc.Node", "_name": "EnemyArea", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 44}, {"__id__": 67}, {"__id__": 90}], "_active": true, "_components": [{"__id__": 113}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 260, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c4CyGHiihFiIYNSKf/AtGU"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 43}, "_prefab": {"__id__": 45}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 44}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 46}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "70ngxeI69CIqMfHclzGZVT", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [{"__id__": 47}], "propertyOverrides": [{"__id__": 50}, {"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 61}, {"__id__": 63}, {"__id__": 65}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 48}, "components": [{"__id__": 49}]}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "d6382ANNPBDjY230JxrBmt5", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 44}}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "nameLabel": null, "hpLabel": null, "mpLabel": null, "actionLabel": null, "hpBar": null, "mpBar": null, "actionBar": null, "characterImage": null, "_id": "2dFt23qYlHUqWboCFfFThj"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 300, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 60, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 62}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 64}, "propertyPath": ["_string"], "value": "哥布林"}, {"__type__": "cc.TargetInfo", "localID": ["RNSV8y5rVBprtoddV0/jt1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 150, "g": 100, "b": 50, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["TpCZtnXeJPOE7wYelUzI9V"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 43}, "_prefab": {"__id__": 68}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 67}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 69}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f14J+hAW5K3bi32t+8Ugt0", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [{"__id__": 70}], "propertyOverrides": [{"__id__": 73}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 71}, "components": [{"__id__": 72}]}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "d6382ANNPBDjY230JxrBmt5", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 67}}, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "nameLabel": null, "hpLabel": null, "mpLabel": null, "actionLabel": null, "hpBar": null, "mpBar": null, "actionBar": null, "characterImage": null, "_id": "30VEkPdf5F36yNbSq1Zrt6"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 100, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 40, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_string"], "value": "兽人"}, {"__type__": "cc.TargetInfo", "localID": ["RNSV8y5rVBprtoddV0/jt1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 100, "g": 150, "b": 100, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["TpCZtnXeJPOE7wYelUzI9V"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 43}, "_prefab": {"__id__": 91}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 92}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6c5M/xOlNDE6F8TE3SF9W9", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [{"__id__": 93}], "propertyOverrides": [{"__id__": 96}, {"__id__": 98}, {"__id__": 99}, {"__id__": 100}, {"__id__": 101}, {"__id__": 103}, {"__id__": 105}, {"__id__": 107}, {"__id__": 109}, {"__id__": 111}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 94}, "components": [{"__id__": 95}]}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "d6382ANNPBDjY230JxrBmt5", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 90}}, "node": {"__id__": 90}, "_enabled": true, "__prefab": null, "nameLabel": null, "hpLabel": null, "mpLabel": null, "actionLabel": null, "hpBar": null, "mpBar": null, "actionBar": null, "characterImage": null, "_id": "58QS8I7qBOW6yfvrwLv9o/"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -100, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 60, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 104}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 106}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 108}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_string"], "value": "哥布林"}, {"__type__": "cc.TargetInfo", "localID": ["RNSV8y5rVBprtoddV0/jt1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 150, "g": 100, "b": 50, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["TpCZtnXeJPOE7wYelUzI9V"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1bs4KVthtPaKUclueJmsHy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c85ac1zChFhoIYbwl9zUth"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "ab96bMsnNHwZnZkFMucVts"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "92KM2X781EH4C1Np0g+a3Y"}, {"__type__": "4ff39kIvYROD71KoejCItzA", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "5f99uPFDlAk4vDL8ZBghNo"}, {"__type__": "de228svI/BK0KuvTnIZOjyz", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "roundLabel": null, "timeLabel": null, "playerArea": null, "enemyArea": null, "_id": "ccbpxaIQVJoY5b54poulKO"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "8cb532e6-3f16-46a1-abb6-86cbe350c139", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 23}, {"__id__": 44}, {"__id__": 67}, {"__id__": 90}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 121}, "shadows": {"__id__": 122}, "_skybox": {"__id__": 123}, "fog": {"__id__": 124}, "octree": {"__id__": 125}, "skin": {"__id__": 126}, "lightProbeInfo": {"__id__": 127}, "postSettings": {"__id__": 128}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 76}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": true, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]
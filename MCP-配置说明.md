# MCP 配置文件说明

我已经为你创建了两个MCP配置文件，用于在Claudia中导入你的MCP工具。

## 📁 创建的文件

### 1. **mcp.json** - 完整配置文件
包含详细的工具列表和配置信息：
- **Context 7**: 2个工具（文档查询和库解析）
- **Playwright**: 24个工具（浏览器自动化）
- **Cocos Creator**: 49个工具（游戏开发）

### 2. **claudia-mcp-config.json** - 简化配置文件
适合Claudia导入的精简版本，只包含基本的服务器配置。

## 🚀 在Claudia中导入配置

### 方法1：使用简化配置（推荐）

1. **启动Claudia**
2. **打开MCP管理器**：Menu → MCP Manager
3. **导入配置**：
   - 点击"Import Configuration"
   - 选择 `claudia-mcp-config.json` 文件
   - 确认导入

### 方法2：手动添加服务器

在Claudia的MCP管理器中手动添加每个服务器：

#### Context 7 服务器
```json
{
  "name": "Context 7",
  "command": "npx",
  "args": ["@context7/mcp-server"],
  "env": {
    "NODE_ENV": "production"
  }
}
```

#### Playwright 服务器
```json
{
  "name": "Playwright",
  "command": "npx", 
  "args": ["@playwright/mcp-server"],
  "env": {
    "PLAYWRIGHT_HEADLESS": "true",
    "NODE_ENV": "production"
  }
}
```

#### Cocos Creator 服务器
```json
{
  "name": "Cocos Creator",
  "command": "node",
  "args": ["./extensions/cocos-mcp-server/dist/mcp-server.js"],
  "env": {
    "COCOS_PROJECT_PATH": "./",
    "MCP_SERVER_PORT": "3000",
    "NODE_ENV": "production"
  }
}
```

## 🔧 配置说明

### Context 7 (2 tools)
- **resolve-library-id**: 解析库名称到Context7兼容的库ID
- **get-library-docs**: 获取库的最新文档

### Playwright (24 tools)
浏览器自动化工具，包括：
- 页面导航和控制
- 元素交互（点击、输入、拖拽）
- 截图和快照
- 标签页管理
- 网络请求监控

### Cocos Creator (49 tools)
游戏开发工具，包括：
- **场景管理** (5 tools): 场景操作、层级结构、执行控制等
- **节点操作** (8 tools): 节点查询、生命周期、变换、层级等
- **组件管理** (3 tools): 组件管理、查询、属性设置
- **预制体** (4 tools): 预制体浏览、生命周期、实例管理
- **项目管理** (2 tools): 项目管理、构建系统
- **调试工具** (3 tools): 控制台、日志、系统信息
- **偏好设置** (3 tools): 设置管理、查询、备份
- **服务器信息** (2 tools): 服务器信息、连接测试
- **广播管理** (2 tools): 日志管理、监听器管理
- **场景视图** (5 tools): 小工具管理、模式控制、相机控制
- **参考图像** (4 tools): 图像管理、查询、变换、显示
- **高级资源** (5 tools): 资源管理、分析、系统、查询、操作
- **验证工具** (3 tools): JSON验证、字符串安全、MCP请求格式化

## ⚠️ 注意事项

### 1. 路径配置
- Cocos Creator服务器的路径是相对于你的项目根目录
- 确保 `extensions/cocos-mcp-server/dist/mcp-server.js` 文件存在

### 2. 环境变量
- Context 7可能需要API密钥，如果需要请添加 `CONTEXT7_API_KEY`
- Playwright设置为无头模式以提高性能

### 3. 端口冲突
- Cocos Creator MCP服务器使用端口3000
- 确保该端口没有被其他服务占用

### 4. 依赖检查
在使用前确保已安装相关依赖：
```bash
# 检查Context 7
npm list @context7/mcp-server

# 检查Playwright
npm list @playwright/mcp-server

# 检查Cocos Creator MCP服务器
# 应该在 extensions/cocos-mcp-server/ 目录中
```

## 🧪 测试配置

导入配置后，在Claudia中：

1. **测试连接**：在MCP管理器中测试每个服务器的连接
2. **验证工具**：确认所有工具都正确加载
3. **创建代理**：使用这些工具创建专用的AI代理

## 📞 故障排除

如果遇到问题：

1. **检查文件路径**：确保所有路径都正确
2. **查看日志**：检查Claudia的控制台输出
3. **测试单独服务器**：逐个测试每个MCP服务器
4. **检查依赖**：确保所有必要的npm包都已安装

## 🎯 使用建议

1. **优先使用简化配置**：`claudia-mcp-config.json` 更容易导入
2. **分步测试**：先导入一个服务器，测试成功后再添加其他
3. **创建专用代理**：为不同的工具集创建不同的AI代理
4. **定期更新**：保持MCP服务器和工具的最新版本

现在你可以将这些配置文件导入到Claudia中，享受强大的MCP工具集成！

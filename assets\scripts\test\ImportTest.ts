import { _decorator, Component } from 'cc';
import { Logger } from '../core/utils/Logger';
import { ISkillData, ISkillResult } from '../data/ISkillData';
import { IEntityData } from '../data/IEntityData';
import { IItemData } from '../data/IItemData';
import { NetworkManager } from '../network/NetworkManager';
import { ApiClient } from '../network/ApiClient';

const { ccclass } = _decorator;

/**
 * 导入测试组件
 * 用于验证所有模块导入是否正常工作
 */
@ccclass('ImportTest')
export class ImportTest extends Component {

    protected onLoad(): void {
        this.testImports();
    }

    /**
     * 测试所有导入
     */
    private testImports(): void {
        Logger.info('🧪 开始导入测试');

        try {
            // 测试Logger
            Logger.debug('Logger导入成功');
            Logger.info('Logger功能正常');
            Logger.warn('Logger警告测试');
            Logger.error('Logger错误测试');

            // 测试数据接口
            const testSkillData: ISkillData = {
                id: 'test_skill',
                name: '测试技能',
                description: '这是一个测试技能',
                manaCost: 10,
                castTime: 1000,
                cooldown: 5000,
                damageType: 'physical',
                targetType: 'enemy',
                baseDamageMultiplier: 1.0
            };
            Logger.info('ISkillData接口测试成功', testSkillData);

            const testEntityData: IEntityData = {
                id: 'test_entity',
                name: '测试实体',
                type: 'player' as any,
                level: 1,
                experience: 0,
                experienceToNext: 100,
                baseAttributes: {
                    health: 100,
                    maxHealth: 100,
                    mana: 50,
                    maxMana: 50,
                    strength: 10,
                    agility: 8,
                    intelligence: 12,
                    constitution: 10,
                    spirit: 8,
                    luck: 5
                },
                combatAttributes: {
                    attack: 15,
                    defense: 10,
                    magicAttack: 12,
                    magicDefense: 8,
                    accuracy: 85,
                    evasion: 15,
                    criticalRate: 5,
                    criticalDamage: 150,
                    attackSpeed: 100,
                    moveSpeed: 100
                },
                position: { x: 0, y: 0 },
                state: 'idle' as any
            };
            Logger.info('IEntityData接口测试成功', testEntityData);

            const testItemData: IItemData = {
                id: 'test_item',
                name: '测试物品',
                description: '这是一个测试物品',
                type: 'weapon' as any,
                rarity: 'common' as any,
                levelRequirement: 1,
                maxStack: 1,
                basePrice: 100,
                sellPrice: 50,
                iconPath: 'test_icon',
                isTradeable: true,
                isDroppable: true,
                isUsable: false
            };
            Logger.info('IItemData接口测试成功', testItemData);

            // 测试NetworkManager
            const networkManager = NetworkManager.getInstance();
            Logger.info('NetworkManager导入成功', { 
                isInitialized: networkManager.isInitialized() 
            });

            // 测试ApiClient
            const apiClient = ApiClient.getInstance();
            Logger.info('ApiClient导入成功', { 
                instance: !!apiClient 
            });

            Logger.info('✅ 所有导入测试通过！');

        } catch (error) {
            Logger.error('❌ 导入测试失败', error);
        }
    }

    /**
     * 测试技能结果接口
     */
    private testSkillResult(): void {
        const testResult: ISkillResult = {
            success: true,
            message: '技能使用成功',
            damage: 50,
            manaConsumed: 10,
            cooldownTime: 5000
        };

        Logger.info('ISkillResult接口测试成功', testResult);
    }

    /**
     * 性能测试
     */
    private performanceTest(): void {
        Logger.time('导入性能测试');
        
        // 模拟一些操作
        for (let i = 0; i < 1000; i++) {
            const data: ISkillData = {
                id: `skill_${i}`,
                name: `技能${i}`,
                description: `描述${i}`,
                manaCost: i,
                castTime: i * 100,
                cooldown: i * 1000,
                damageType: 'physical',
                targetType: 'enemy',
                baseDamageMultiplier: 1.0 + i * 0.1
            };
        }
        
        Logger.timeEnd('导入性能测试');
    }

    protected start(): void {
        // 延迟执行性能测试
        this.scheduleOnce(() => {
            this.performanceTest();
        }, 1);
    }
}

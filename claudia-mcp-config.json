{"mcpServers": {"context7": {"command": "npx", "args": ["@context7/mcp-server"], "env": {"NODE_ENV": "production"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp-server"], "env": {"PLAYWRIGHT_HEADLESS": "true", "NODE_ENV": "production"}}, "cocos-creator": {"command": "node", "args": ["./extensions/cocos-mcp-server/dist/mcp-server.js"], "env": {"COCOS_PROJECT_PATH": "./", "MCP_SERVER_PORT": "3000", "NODE_ENV": "production"}}}}
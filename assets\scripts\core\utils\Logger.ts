/**
 * 前端日志工具类
 * 提供统一的日志记录功能
 */

export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    NONE = 4
}

export interface LogConfig {
    level: LogLevel;
    enableConsole: boolean;
    enableRemote: boolean;
    remoteEndpoint?: string;
    maxLogEntries: number;
}

export interface LogEntry {
    timestamp: string;
    level: LogLevel;
    message: string;
    data?: any;
    source?: string;
}

export class Logger {
    private static config: LogConfig = {
        level: LogLevel.INFO,
        enableConsole: true,
        enableRemote: false,
        maxLogEntries: 1000
    };

    private static logEntries: LogEntry[] = [];
    private static context: { [key: string]: any } = {};

    /**
     * 设置日志配置
     */
    public static setConfig(config: Partial<LogConfig>): void {
        Logger.config = { ...Logger.config, ...config };
    }

    /**
     * 设置日志上下文
     */
    public static setContext(context: { [key: string]: any }): void {
        Logger.context = { ...Logger.context, ...context };
    }

    /**
     * 清除日志上下文
     */
    public static clearContext(): void {
        Logger.context = {};
    }

    /**
     * 创建日志条目
     */
    private static createLogEntry(level: LogLevel, message: string, data?: any, source?: string): LogEntry {
        return {
            timestamp: new Date().toISOString(),
            level,
            message,
            data: data ? { ...Logger.context, ...data } : Logger.context,
            source
        };
    }

    /**
     * 记录日志
     */
    private static log(level: LogLevel, message: string, data?: any, source?: string): void {
        // 检查日志级别
        if (level < Logger.config.level) {
            return;
        }

        const logEntry = Logger.createLogEntry(level, message, data, source);

        // 控制台输出
        if (Logger.config.enableConsole) {
            Logger.logToConsole(logEntry);
        }

        // 存储日志条目
        Logger.logEntries.push(logEntry);

        // 限制日志条目数量
        if (Logger.logEntries.length > Logger.config.maxLogEntries) {
            Logger.logEntries.shift();
        }

        // 远程日志（如果启用）
        if (Logger.config.enableRemote && Logger.config.remoteEndpoint) {
            Logger.sendToRemote(logEntry);
        }
    }

    /**
     * 控制台输出
     */
    private static logToConsole(entry: LogEntry): void {
        const timestamp = new Date(entry.timestamp).toLocaleTimeString();
        const levelName = LogLevel[entry.level];
        const prefix = `[${timestamp}] [${levelName}]`;

        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(prefix, entry.message, entry.data);
                break;
            case LogLevel.INFO:
                console.info(prefix, entry.message, entry.data);
                break;
            case LogLevel.WARN:
                console.warn(prefix, entry.message, entry.data);
                break;
            case LogLevel.ERROR:
                console.error(prefix, entry.message, entry.data);
                break;
        }
    }

    /**
     * 发送到远程服务器
     */
    private static async sendToRemote(entry: LogEntry): Promise<void> {
        try {
            if (!Logger.config.remoteEndpoint) return;

            await fetch(Logger.config.remoteEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entry)
            });
        } catch (error) {
            // 远程日志失败时不应该影响应用运行
            console.warn('Failed to send log to remote:', error);
        }
    }

    /**
     * 调试日志
     */
    public static debug(message: string, data?: any, source?: string): void {
        Logger.log(LogLevel.DEBUG, message, data, source);
    }

    /**
     * 信息日志
     */
    public static info(message: string, data?: any, source?: string): void {
        Logger.log(LogLevel.INFO, message, data, source);
    }

    /**
     * 警告日志
     */
    public static warn(message: string, data?: any, source?: string): void {
        Logger.log(LogLevel.WARN, message, data, source);
    }

    /**
     * 错误日志
     */
    public static error(message: string, error?: any, source?: string): void {
        let errorData: any = {};

        if (error instanceof Error) {
            errorData = {
                name: error.name,
                message: error.message,
                stack: error.stack
            };
        } else if (error) {
            errorData = { error };
        }

        Logger.log(LogLevel.ERROR, message, errorData, source);
    }

    /**
     * 获取所有日志条目
     */
    public static getLogEntries(): LogEntry[] {
        return [...Logger.logEntries];
    }

    /**
     * 获取指定级别的日志条目
     */
    public static getLogEntriesByLevel(level: LogLevel): LogEntry[] {
        return Logger.logEntries.filter(entry => entry.level === level);
    }

    /**
     * 清除所有日志条目
     */
    public static clearLogEntries(): void {
        Logger.logEntries = [];
    }

    /**
     * 获取日志统计信息
     */
    public static getStats(): { [key: string]: number } {
        const stats: { [key: string]: number } = {};
        
        Object.values(LogLevel).forEach(level => {
            if (typeof level === 'number') {
                const levelName = LogLevel[level];
                stats[levelName] = Logger.logEntries.filter(entry => entry.level === level).length;
            }
        });

        return stats;
    }

    /**
     * 导出日志为JSON
     */
    public static exportLogs(): string {
        return JSON.stringify({
            config: Logger.config,
            context: Logger.context,
            entries: Logger.logEntries,
            stats: Logger.getStats(),
            exportTime: new Date().toISOString()
        }, null, 2);
    }

    /**
     * 性能计时开始
     */
    private static timers: Map<string, number> = new Map();

    public static time(label: string): void {
        Logger.timers.set(label, performance.now());
    }

    /**
     * 性能计时结束
     */
    public static timeEnd(label: string): void {
        const startTime = Logger.timers.get(label);
        if (startTime !== undefined) {
            const duration = performance.now() - startTime;
            Logger.info(`Timer ${label}`, { duration: `${duration.toFixed(2)}ms` });
            Logger.timers.delete(label);
        }
    }

    /**
     * 创建带特定上下文的子Logger
     */
    public static createChild(context: { [key: string]: any }): typeof Logger {
        const childLogger = Object.create(Logger);
        childLogger.context = { ...Logger.context, ...context };
        return childLogger;
    }
}

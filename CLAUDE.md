# CLAUDE.md

必须用中文回复我

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multiplayer idle/incremental game built with **Cocos Creator 3.8.6** and **TypeScript**, targeting WeChat Mini Program and Douyin Mini Program platforms. The project is migrated from Godot 4.4 to Cocos Creator for better mobile/mini-program optimization.

**Core Game Features:**
- Idle/incremental gameplay with offline progression
- Character development system with skills and equipment
- Turn-based combat system
- Social features (guilds, friends, chat, rankings)
- Crafting and trading systems

## Development Commands

### Frontend (Cocos Creator)
```bash
# Development
npm run dev                    # Start Cocos Creator in dev mode
npm run build                  # Build project
npm run build:wechat          # Build for WeChat Mini Program
npm run build:douyin          # Build for Douyin Mini Program

# Testing
npm run test                   # Run Jest unit tests
npm run test:watch            # Run tests in watch mode
npm run test:coverage         # Run tests with coverage
npm run test:ai               # Run AI-assisted testing
npm run test:ai:simple        # Run simple AI tests

# Code Quality
npm run lint                   # Lint TypeScript files
npm run lint:fix              # Auto-fix linting issues
npm run format                # Format code with Prettier
npm run fix:quality           # Fix code quality issues

# Utilities
npm run clean                 # Clean build/temp/library directories
npm run dev:full              # Run test server and AI tests together
npm run server:test           # Run simple test server
npm run fix:quality           # Run code quality fixer
```

### Backend
```bash
cd backend

# Development
npm run dev                   # Start development server with nodemon
npm run build                 # Build TypeScript to JavaScript
npm run start                 # Start production server

# Testing
npm run test                  # Run Jest tests
npm run test:watch           # Run tests in watch mode
npm run test:ai              # Run AI service tests
npm run test:integration     # Run integration tests
npm run test:coverage        # Run tests with coverage

# Code Quality
npm run lint                 # Lint backend code
npm run lint:fix             # Auto-fix linting issues
npm run format               # Format code with Prettier
```

## Architecture Overview

### Core Manager System
The project uses a singleton-based manager architecture with these core managers:

- **GameManager**: Central game state and lifecycle management
- **SceneManager**: Scene loading and transitions
- **EventManager**: Event-driven communication system
- **ResourceManager**: Asset loading and caching
- **ConfigManager**: Configuration data management
- **SkillManager**: Skill system management
- **UIManager**: UI panel and component management
- **AudioManager**: Sound and music management
- **NetworkManager**: Client-server communication
- **InputManager**: Input handling and processing

### Project Structure
```
assets/scripts/
├── core/              # Core utilities and base classes
├── managers/          # Singleton manager classes
├── systems/           # Game systems (skill, character, etc.)
├── ui/               # UI components and panels
├── scenes/           # Scene controllers
├── data/             # Data types and interfaces
├── network/          # Network communication
├── config/           # Configuration interfaces
└── test/             # Test files
```

### Manager Initialization
All managers follow a specific initialization order defined in `assets/scripts/managers/index.ts`:
1. EventManager
2. ResourceManager  
3. ConfigManager
4. SkillManager
5. UIManager
6. AudioManager
7. InputManager
8. NetworkManager
9. SceneManager
10. GameManager

### TypeScript Configuration
- **Target**: ES2020
- **Module**: ES2020
- **Strict Mode**: Disabled (for Cocos Creator compatibility)
- **Decorators**: Enabled (experimental decorators and metadata)
- **Path Aliases**: 
  - `@/*` → `./assets/scripts/*`
  - `@core/*` → `./assets/scripts/core/*`
  - `@systems/*` → `./assets/scripts/systems/*`
  - `@ui/*` → `./assets/scripts/ui/*`
  - `@data/*` → `./assets/scripts/data/*`
  - `@scenes/*` → `./assets/scripts/scenes/*`

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Database**: MongoDB with Mongoose
- **Cloud Services**: WeChat Cloud Development, Douyin Cloud Services
- **Real-time**: Socket.io for WebSocket communication
- **Authentication**: JWT tokens
- **Caching**: Redis for session and data caching

### Build Targets
- **Primary**: WeChat Mini Program (`wechatgame`)
- **Secondary**: Douyin Mini Program (`bytedance-mini-game`)
- **Development**: Web browser for testing

## Key Development Patterns

### Manager Pattern
All core systems inherit from `BaseManager` and use singleton pattern:
```typescript
export class MyManager extends BaseManager {
    private static _instance: MyManager;
    
    public static getInstance(): MyManager {
        if (!this._instance) {
            this._instance = new MyManager();
        }
        return this._instance;
    }
}
```

### Event-Driven Communication
Use `EventManager` for decoupled system communication:
```typescript
// Emit events
EventManager.getInstance().emit('SKILL_LEARNED', skillData);

// Listen to events  
EventManager.getInstance().on('LEVEL_UP', this.onLevelUp.bind(this));
```

### UI Component System
UI components extend base classes with consistent lifecycle:
- `BaseUIPanel` for modal panels
- `BaseUIComponent` for reusable components
- `EnhancedBasePanel` for advanced panel features

### Configuration Data
Game configuration uses JSON files in `assets/resources/config/`:
- `skills.json` - Skill definitions
- `items.json` - Item data
- `rewards.json` - Reward configurations

### Testing Strategy
- **Unit Tests**: Jest for manager and system testing
- **Integration Tests**: Custom test framework for system interactions
- **AI Testing**: Automated testing with AI assistance
- **Manual Testing**: UI and gameplay validation

## Platform-Specific Notes

### WeChat Mini Program
- Uses WeChat Cloud Development backend
- Implements WeChat-specific UI adaptations
- Handles WeChat payment and social features

### Douyin Mini Program
- Uses Douyin Cloud Services
- Adapts to Douyin platform requirements
- Integrates with Douyin social features

## Development Workflow

1. **Feature Development**: Create in appropriate system/manager
2. **UI Integration**: Add UI components in `ui/` directory
3. **Configuration**: Add data to JSON config files
4. **Testing**: Write unit tests and run AI testing
5. **Build Validation**: Test on target platforms
6. **Code Quality**: Run lint, format, and quality checks

## Important Notes

- All managers must be properly initialized before use
- Use TypeScript path aliases for clean imports
- Follow singleton pattern for managers
- Implement proper error handling and logging
- Test on both WeChat and Douyin platforms before deployment
- Maintain configuration data separation from code logic
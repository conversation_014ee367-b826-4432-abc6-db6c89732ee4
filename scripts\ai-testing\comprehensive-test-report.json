{"metadata": {"generatedAt": "2025-08-03T11:45:15.781Z", "projectPath": "D:\\COCOS\\Projects\\IdleGame\\COCOS_IdleGame", "totalExecutionTime": 413, "testFramework": "AI-Testing-Framework-v1.0"}, "summary": {"totalTests": 12, "passedTests": 11, "failedTests": 1, "successRate": 91.7, "executionTime": 413}, "results": [{"name": "项目结构检查", "status": "passed", "message": "项目结构完整 (发现 6 个必需项目)", "details": {"found": ["project.json", "assets", "settings", "assets/scripts", "assets/scenes", "assets/resources"], "missing": []}}, {"name": "Cocos Creator配置", "status": "passed", "message": "配置检查 4/4 项通过 (引擎: cocos-creator)", "details": {"config": {"hasVersion": true, "hasEngine": true, "hasName": true, "validEngine": true}, "raw": {"engine": "cocos-creator", "packages": "packages", "version": "3.8.6", "name": "<PERSON><PERSON><PERSON><PERSON>", "id": "d7d09da7-3476-4f62-91cb-61d527ea4910", "settings": {"assets": {"server": "", "bundleVer": "1.0.0"}, "rendering": {"renderMode": 0, "renderPipeline": "builtin-forward"}, "physics": {"enabled": true, "gravity": {"x": 0, "y": -10}}, "scripting": {"typescript": true, "allowDeclareKeyword": true}, "animation": {"enabled": true}, "audio": {"enabled": true}, "network": {"enabled": true}}, "platforms": {"wechatgame": {"enabled": true, "orientation": "portrait", "separateEngine": true, "startScene": "Launch"}, "bytedance-mini-game": {"enabled": true, "orientation": "portrait", "separateEngine": true, "startScene": "Launch"}}}}}, {"name": "脚本文件分析", "status": "passed", "message": "发现 99 个脚本文件，76 个组件，71 个系统", "details": {"totalFiles": 99, "components": 76, "systems": 71, "totalLines": 35069, "fileTypes": {".ts": 99}, "averageLinesPerFile": 354}}, {"name": "资源文件检查", "status": "passed", "message": "发现 11 个资源文件，总大小 7.85 MB", "details": {"assets": {"images": 0, "prefabs": 6, "scenes": 2, "data": 3, "audio": 0}, "totalSize": 8231873, "totalAssets": 11}}, {"name": "后端服务器状态", "status": "passed", "message": "服务器运行正常 (状态码: 200)", "details": {"statusCode": 200, "response": "{\"status\":\"ok\",\"message\":\"Test server is running\",\"timestamp\":\"2025-08-03T11:45:15.550Z\",\"uptime\":3.2769126}"}}, {"name": "游戏逻辑验证", "status": "passed", "message": "游戏逻辑测试 5/5 项通过", "details": {"tests": [{"name": "角色创建", "result": true}, {"name": "战斗系统", "result": true}, {"name": "技能系统", "result": true}, {"name": "装备系统", "result": true}, {"name": "经验计算", "result": true}], "passed": 5, "total": 5}}, {"name": "性能基准测试", "status": "passed", "message": "基准测试完成，耗时 110ms", "details": {"duration": 110, "benchmark": "basic", "threshold": 200}}, {"name": "代码质量分析", "status": "failed", "message": "代码质量: needs-improvement，发现 95 个问题", "details": {"totalFiles": 99, "totalLines": 35069, "issuesFound": 95, "quality": "needs-improvement"}}, {"name": "组件依赖检查", "status": "passed", "message": "分析了 79 个依赖关系，发现 0 个问题", "details": {"dependencies": {"cc": ["TestRunner.ts", "AudioManager.ts", "BaseManager.ts", "ConfigManager.ts", "ConfigManager.ts", "EventManager.ts", "GameManager.ts", "InputManager.ts", "ResourceManager.ts", "SceneManager.ts", "SkillManager.ts", "UIManager.ts", "HttpClient.ts", "NetworkManager.ts", "WebSocketClient.ts", "BattleScene.ts", "BattleSceneClean.ts", "LaunchScene.ts", "MainScene.ts", "Character.ts", "BasicTest.ts", "ConfigManagerQuickTest.ts", "ConfigManagerTest.ts", "Day2IntegrationTest.ts", "EmergencyKeyboardTest.ts", "ImportTest.ts", "KeyboardInputTest.ts", "ManagerTest.ts", "MobileIdleUITest.ts", "NetworkTest.ts", "PerformanceBenchmarkTest.ts", "QuickConfigVerify.ts", "SimpleConfigTest.ts", "SimpleKeyboardTest.ts", "SimpleLaunchTest.ts", "SimpleTest.ts", "SimpleUIPanelRegistrar.ts", "StandaloneTest.ts", "SystemIntegrationTest.ts", "TestManager.ts", "UIFrameworkValidator.ts", "UIInteractionTest.ts", "UISystemTest.ts", "BaseUIComponent.ts", "BaseUIPanel.ts", "EnhancedBasePanel.ts", "MultiEffectButton.ts", "BottomPanelController.ts", "ActionBar.ts", "BattleUIController.ts", "CharacterUnit.ts", "GridListManager.ts", "SceneSwitchButtons.ts", "SkillBar.ts", "SkillBarUI.ts", "SkillDisplayBar.ts", "SkillSlot.ts", "UIButton.ts", "UIDialog.ts", "UIPanel.ts", "UIPanelConfigurator.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "MainUIController.ts", "AreaSelectionPanel.ts", "BehaviorPanel.ts", "BehaviorPanelSimple.ts", "CharacterInfoPanel.ts", "EquipmentPanel.ts", "InventoryPanel.ts", "SkillPanel.ts", "SkillSelectionPanel.ts", "SkillBarUI.ts", "SkillDetailView.ts", "SkillListItem.ts", "SkillSelectionPanel.ts", "SkillSlot.ts", "UITypes.ts"], "../../managers/GameManager": ["TestRunner.ts"], "../../data/GameTypes": ["TestRunner.ts", "Character.ts"], "./BaseManager": ["AudioManager.ts", "ConfigManager.ts", "EventManager.ts", "GameManager.ts", "index.ts", "InputManager.ts", "ResourceManager.ts", "SceneManager.ts", "SkillManager.ts", "UIManager.ts"], "../config/interfaces/ISkillData": ["ConfigManager.ts", "ConfigManagerTest.ts"], "../config/interfaces/IEntityData": ["ConfigManager.ts"], "../config/interfaces/IItemData": ["ConfigManager.ts"], "../config/interfaces/IQuestData": ["ConfigManager.ts"], "../config/interfaces/IRewardData": ["ConfigManager.ts"], "./types/ManagerTypes": ["EventManager.ts", "GameManager.ts", "ResourceManager.ts", "SceneManager.ts"], "./GameManager": ["index.ts"], "./SceneManager": ["index.ts"], "./EventManager": ["index.ts", "SkillManager.ts", "UIManager.ts"], "./ResourceManager": ["index.ts"], "./AudioManager": ["index.ts"], "./InputManager": ["index.ts"], "../network/NetworkManager": ["index.ts", "BattleScene.ts", "ImportTest.ts", "NetworkTest.ts"], "./ConfigManager": ["index.ts", "SkillManager.ts"], "./UIManager": ["index.ts"], "./SkillManager": ["index.ts"], "./NetworkManager": ["ApiClient.ts", "index.ts"], "../data/ISkillData": ["ApiClient.ts", "DataSyncManager.ts", "ImportTest.ts"], "../data/IEntityData": ["ApiClient.ts", "DataSyncManager.ts", "ImportTest.ts"], "../data/IItemData": ["ApiClient.ts", "DataSyncManager.ts", "ImportTest.ts"], "../core/utils/Logger": ["ApiClient.ts", "DataSyncManager.ts", "ImportTest.ts"], "../managers/BaseManager": ["DataSyncManager.ts", "NetworkManager.ts"], "./ApiClient": ["DataSyncManager.ts"], "../managers/EventManager": ["DataSyncManager.ts", "HttpClient.ts", "NetworkManager.ts", "WebSocketClient.ts", "BattleScene.ts", "BattleSceneClean.ts", "MainScene.ts", "NetworkTest.ts", "SystemIntegrationTest.ts", "UIInteractionTest.ts", "UISystemTest.ts"], "./HttpClient": ["index.ts", "NetworkManager.ts"], "./WebSocketClient": ["index.ts", "NetworkManager.ts"], "../network/HttpClient": ["BattleScene.ts", "NetworkTest.ts"], "../network/WebSocketClient": ["BattleScene.ts", "NetworkTest.ts"], "../network/index": ["BattleScene.ts", "NetworkTest.ts"], "../network/types/NetworkTypes": ["BattleScene.ts", "NetworkTest.ts", "SystemIntegrationTest.ts"], "../managers/index": ["BattleScene.ts", "SystemIntegrationTest.ts"], "../managers/UIManager": ["BattleSceneClean.ts", "MainScene.ts", "MobileIdleUITest.ts", "SimpleUIPanelRegistrar.ts", "UIFrameworkValidator.ts", "UIInteractionTest.ts", "UISystemTest.ts", "MainUIController.ts"], "../ui/input/MobileUIInputHandler": ["BattleSceneClean.ts", "MobileIdleUITest.ts"], "../test/SimpleUIPanelRegistrar": ["BattleSceneClean.ts"], "../managers": ["LaunchScene.ts", "EmergencyKeyboardTest.ts", "ManagerTest.ts", "StandaloneTest.ts"], "../managers/GameManager": ["MainScene.ts", "Day2IntegrationTest.ts", "SimpleTest.ts"], "../ui/panels/AreaSelectionPanel": ["MainScene.ts"], "../ui/panels/CharacterInfoPanel": ["MainScene.ts"], "../../config/interfaces/ISkillData": ["SkillCalculationService.ts", "SkillEffectSystem.ts", "SkillPanel.ts", "SkillBarUI.ts", "SkillDetailView.ts", "SkillListItem.ts", "SkillSelectionPanel.ts", "SkillSlot.ts"], "../managers/ConfigManager": ["ConfigManagerQuickTest.ts", "ConfigManagerTest.ts", "QuickConfigVerify.ts", "SimpleConfigTest.ts"], "../systems/characters/Character": ["Day2IntegrationTest.ts"], "../data/GameTypes": ["Day2IntegrationTest.ts"], "../network/ApiClient": ["ImportTest.ts"], "../ui/types/UITypes": ["MobileIdleUITest.ts", "SimpleUIPanelRegistrar.ts", "UIFrameworkValidator.ts", "UIInteractionTest.ts", "UISystemTest.ts"], "../ui/components/SkillDisplayBar": ["MobileIdleUITest.ts"], "./TestManager": ["PerformanceBenchmarkTest.ts"], "../ui/base/BaseUIComponent": ["UIFrameworkValidator.ts"], "../ui/base/EnhancedBasePanel": ["UIFrameworkValidator.ts"], "../ui/components/SkillBarUI": ["UIFrameworkValidator.ts"], "../ui/components/SkillSlot": ["UIFrameworkValidator.ts"], "../ui/panels/SkillSelectionPanel": ["UIFrameworkValidator.ts"], "../ui/input/UIInputHandler": ["UISystemTest.ts"], "../../managers/EventManager": ["BaseUIComponent.ts", "EnhancedBasePanel.ts", "SkillBar.ts", "SkillBarUI.ts", "SkillDisplayBar.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "AreaSelectionPanel.ts", "BehaviorPanel.ts", "CharacterInfoPanel.ts", "SkillSelectionPanel.ts", "SkillBarUI.ts", "SkillSelectionPanel.ts"], "../types/UITypes": ["EnhancedBasePanel.ts", "UIButton.ts", "UIDialog.ts", "UIPanel.ts", "UIPanelConfigurator.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "EquipmentPanel.ts", "InventoryPanel.ts", "SkillPanel.ts", "SkillSelectionPanel.ts", "SkillSelectionPanel.ts"], "./CharacterUnit": ["BattleUIController.ts"], "./UIButton": ["SkillBar.ts", "SkillDisplayBar.ts", "UIDialog.ts", "UIPanel.ts"], "../panels/SkillPanel": ["SkillBar.ts", "SkillBarUI.ts", "SkillDisplayBar.ts", "SkillSlot.ts"], "../../managers/ConfigManager": ["SkillBar.ts", "InventoryPanel.ts", "SkillPanel.ts", "SkillSelectionPanel.ts", "SkillBarUI.ts", "SkillSelectionPanel.ts"], "../base/BaseUIComponent": ["SkillBarUI.ts", "SkillSlot.ts", "SkillBarUI.ts", "SkillDetailView.ts", "SkillListItem.ts", "SkillSlot.ts"], "../../managers/AudioManager": ["UIButton.ts"], "./UIPanel": ["UIDialog.ts"], "../base/BaseUIPanel": ["UIPanel.ts", "AreaSelectionPanel.ts", "CharacterInfoPanel.ts", "EquipmentPanel.ts", "SkillSelectionPanel.ts"], "../../managers/UIManager": ["UIPanelConfigurator.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "SkillSelectionPanel.ts"], "./types/UITypes": ["MainUIController.ts"], "../components/ActionBar": ["BehaviorPanel.ts"], "../components/UIPanel": ["InventoryPanel.ts", "SkillPanel.ts"], "../components/UIButton": ["InventoryPanel.ts", "SkillPanel.ts"], "../../config/interfaces/IItemData": ["InventoryPanel.ts"], "../base/EnhancedBasePanel": ["SkillSelectionPanel.ts"], "./SkillPanel": ["SkillSelectionPanel.ts"], "./SkillSlot": ["SkillBarUI.ts"], "../../managers/SkillManager": ["SkillBarUI.ts", "SkillSelectionPanel.ts"], "../../managers/ResourceManager": ["SkillDetailView.ts", "SkillListItem.ts", "SkillSlot.ts"], "./SkillListItem": ["SkillSelectionPanel.ts"], "./SkillDetailView": ["SkillSelectionPanel.ts"]}, "missingDeps": []}}, {"name": "场景完整性验证", "status": "passed", "message": "检查了 2 个场景，2 个有效", "details": {"scenes": [{"name": "Battle.scene", "hasNodes": false, "nodeCount": 0, "hasComponents": true}, {"name": "Main.scene", "hasNodes": false, "nodeCount": 0, "hasComponents": true}], "totalScenes": 2, "validScenes": 2}}, {"name": "资源引用检查", "status": "passed", "message": "检查了 191 个资源，发现 0 个损坏引用", "details": {"totalAssets": 191, "brokenReferences": []}}, {"name": "武侠系统测试", "status": "passed", "message": "武侠系统实现度 5/5，发现 50 个相关文件", "details": {"systems": {"hasCharacterSystem": true, "hasSkillSystem": true, "hasBattleSystem": true, "hasCultivationSystem": true, "hasSectSystem": true}, "relatedFiles": 50, "implementedSystems": 5}}], "recommendations": ["⚠️ 发现 1 个问题需要解决：", "   • 代码质量分析: 代码质量: needs-improvement，发现 95 个问题"]}
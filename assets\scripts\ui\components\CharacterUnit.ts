import { _decorator, Component, Node, Label, ProgressBar, Sprite, Color, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 角色单元组件
 * 控制单个角色的显示和行为
 */
@ccclass('CharacterUnit')
export class CharacterUnit extends Component {
    
    @property(Label)
    nameLabel: Label = null!;
    
    @property(Label)
    hpLabel: Label = null!;
    
    @property(Label)
    mpLabel: Label = null!;
    
    @property(Label)
    actionLabel: Label = null!;
    
    @property(ProgressBar)
    hpBar: ProgressBar = null!;
    
    @property(ProgressBar)
    mpBar: ProgressBar = null!;
    
    @property(ProgressBar)
    actionBar: ProgressBar = null!;
    
    @property(Sprite)
    characterImage: Sprite = null!;
    
    // 角色属性
    private characterName: string = '战士';
    private maxHp: number = 210;
    private currentHp: number = 210;
    private maxMp: number = 20;
    private currentMp: number = 20;
    private actionProgress: number = 0;
    private actionSpeed: number = 0.1; // 行动条增长速度
    private team: string = 'player';
    private isAlive: boolean = true;
    
    start() {
        this.initializeUnit();
        this.autoFindComponents();
    }
    
    /**
     * 自动查找组件
     */
    private autoFindComponents() {
        // 自动查找NameLabel
        if (!this.nameLabel) {
            const nameNode = this.node.getChildByName('NameLabel');
            if (nameNode) {
                this.nameLabel = nameNode.getComponent(Label);
            }
        }
        
        // 自动查找HP相关组件
        const hpBarBg = this.node.getChildByName('HPBarBg');
        if (hpBarBg) {
            if (!this.hpLabel) {
                const hpLabelNode = hpBarBg.getChildByName('HPLabel');
                if (hpLabelNode) {
                    this.hpLabel = hpLabelNode.getComponent(Label);
                }
            }
            
            if (!this.hpBar) {
                const hpBarNode = hpBarBg.getChildByName('HPBar');
                if (hpBarNode) {
                    this.hpBar = hpBarNode.getComponent(ProgressBar);
                }
            }
        }
        
        // 自动查找MP相关组件
        const mpBarBg = this.node.getChildByName('MPBarBg');
        if (mpBarBg) {
            if (!this.mpLabel) {
                const mpLabelNode = mpBarBg.getChildByName('MPLabel');
                if (mpLabelNode) {
                    this.mpLabel = mpLabelNode.getComponent(Label);
                }
            }
            
            if (!this.mpBar) {
                const mpBarNode = mpBarBg.getChildByName('MPBar');
                if (mpBarNode) {
                    this.mpBar = mpBarNode.getComponent(ProgressBar);
                }
            }
        }
        
        // 自动查找Action相关组件
        const actionBarBg = this.node.getChildByName('ActionBarBg');
        if (actionBarBg) {
            if (!this.actionLabel) {
                const actionLabelNode = actionBarBg.getChildByName('ActionLabel');
                if (actionLabelNode) {
                    this.actionLabel = actionLabelNode.getComponent(Label);
                }
            }
            
            if (!this.actionBar) {
                const actionBarNode = actionBarBg.getChildByName('ActionBar');
                if (actionBarNode) {
                    this.actionBar = actionBarNode.getComponent(ProgressBar);
                }
            }
        }
        
        // 自动查找角色图像
        if (!this.characterImage) {
            const imageNode = this.node.getChildByName('CharacterImage');
            if (imageNode) {
                this.characterImage = imageNode.getComponent(Sprite);
            }
        }
    }
    
    /**
     * 初始化角色单元
     */
    private initializeUnit() {
        this.updateDisplay();
    }
    
    /**
     * 设置队伍
     */
    public setTeam(team: string) {
        this.team = team;
        
        // 根据队伍设置不同的外观
        if (team === 'enemy') {
            // 敌人使用不同的颜色
            if (this.characterImage) {
                this.characterImage.color = new Color(150, 100, 50, 255);
            }
        }
    }
    
    /**
     * 更新角色单元
     */
    public updateUnit(deltaTime: number) {
        if (!this.isAlive) return;
        
        // 更新行动条
        this.actionProgress += this.actionSpeed * deltaTime;
        if (this.actionProgress >= 1.0) {
            this.actionProgress = 1.0;
            this.performAction();
        }
        
        this.updateDisplay();
    }
    
    /**
     * 执行行动
     */
    private performAction() {
        console.log(`${this.characterName} 执行行动`);
        
        // 重置行动条
        this.actionProgress = 0;
        
        // TODO: 实现具体的行动逻辑（攻击、技能等）
    }
    
    /**
     * 更新显示
     */
    private updateDisplay() {
        // 更新名称
        if (this.nameLabel) {
            this.nameLabel.string = this.characterName;
        }
        
        // 更新HP显示
        if (this.hpLabel) {
            this.hpLabel.string = `${this.currentHp}/${this.maxHp}`;
        }
        if (this.hpBar) {
            this.hpBar.progress = this.currentHp / this.maxHp;
        }
        
        // 更新MP显示
        if (this.mpLabel) {
            this.mpLabel.string = `${this.currentMp}/${this.maxMp}`;
        }
        if (this.mpBar) {
            this.mpBar.progress = this.currentMp / this.maxMp;
        }
        
        // 更新行动条显示
        if (this.actionBar) {
            this.actionBar.progress = this.actionProgress;
        }
    }
    
    /**
     * 设置角色数据
     */
    public setCharacterData(name: string, maxHp: number, maxMp: number) {
        this.characterName = name;
        this.maxHp = maxHp;
        this.currentHp = maxHp;
        this.maxMp = maxMp;
        this.currentMp = maxMp;
        this.updateDisplay();
    }
    
    /**
     * 受到伤害
     */
    public takeDamage(damage: number) {
        this.currentHp = Math.max(0, this.currentHp - damage);
        
        if (this.currentHp <= 0) {
            this.die();
        }
        
        // 伤害数字动画
        this.showDamageNumber(damage);
        
        this.updateDisplay();
    }
    
    /**
     * 显示伤害数字
     */
    private showDamageNumber(damage: number) {
        // TODO: 实现伤害数字飘字效果
        console.log(`${this.characterName} 受到 ${damage} 点伤害`);
    }
    
    /**
     * 死亡
     */
    private die() {
        this.isAlive = false;
        console.log(`${this.characterName} 死亡`);
        
        // 死亡动画
        tween(this.node)
            .to(0.5, { scale: new Vec3(0.8, 0.8, 1) })
            .call(() => {
                this.node.active = false;
            })
            .start();
    }
    
    /**
     * 获取角色状态
     */
    public getCharacterInfo() {
        return {
            name: this.characterName,
            hp: this.currentHp,
            maxHp: this.maxHp,
            mp: this.currentMp,
            maxMp: this.maxMp,
            actionProgress: this.actionProgress,
            team: this.team,
            isAlive: this.isAlive
        };
    }
}
